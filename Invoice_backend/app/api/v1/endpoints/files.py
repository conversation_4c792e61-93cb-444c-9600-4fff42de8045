"""
File serving endpoints for uploaded invoices and attachments.
"""

import logging
from pathlib import Path
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import FileResponse
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/invoices/{filename}")
async def serve_invoice_file(filename: str):
    """
    Serve uploaded invoice files.

    Args:
        filename: The filename of the invoice file to serve

    Returns:
        FileResponse: The requested file

    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        # Construct the file path
        file_path = Path(settings.PROJECT_ROOT) / "storage" / "invoices" / filename

        # Security check: ensure the file is within the allowed directory
        storage_dir = Path(settings.PROJECT_ROOT) / "storage" / "invoices"
        try:
            file_path.resolve().relative_to(storage_dir.resolve())
        except ValueError:
            logger.warning(f"Attempted access to file outside storage directory: {filename}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Check if file exists
        if not file_path.exists():
            logger.warning(f"Requested file not found: {file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Check if it's actually a file (not a directory)
        if not file_path.is_file():
            logger.warning(f"Requested path is not a file: {file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        logger.info(f"Serving invoice file: {filename}")

        # Return the file
        return FileResponse(
            path=str(file_path),
            filename=filename,
            media_type="application/pdf"  # Assuming PDF files for invoices
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error serving file {filename}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/attachments/{connection_id}/{email_id}/{filename}")
async def serve_attachment_file(connection_id: str, email_id: str, filename: str):
    """
    Serve uploaded attachment files with nested path structure.

    Args:
        connection_id: The connection ID
        email_id: The email ID
        filename: The filename of the attachment file to serve

    Returns:
        FileResponse: The requested file

    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        # Construct the file path with nested structure
        file_path = Path(settings.PROJECT_ROOT) / "storage" / "attachments" / connection_id / email_id / filename

        # Security check: ensure the file is within the allowed directory
        storage_dir = Path(settings.PROJECT_ROOT) / "storage" / "attachments"
        try:
            file_path.resolve().relative_to(storage_dir.resolve())
        except ValueError:
            logger.warning(f"Attempted access to attachment file outside storage directory: {connection_id}/{email_id}/{filename}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Check if file exists
        if not file_path.exists():
            logger.warning(f"Requested attachment file not found: {file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Check if it's actually a file (not a directory)
        if not file_path.is_file():
            logger.warning(f"Requested attachment path is not a file: {file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        logger.info(f"Serving attachment file: {filename}")

        # Determine media type based on file extension
        media_type = "application/octet-stream"  # Default
        suffix = file_path.suffix.lower()
        if suffix == ".pdf":
            media_type = "application/pdf"
        elif suffix in [".jpg", ".jpeg"]:
            media_type = "image/jpeg"
        elif suffix == ".png":
            media_type = "image/png"
        elif suffix == ".txt":
            media_type = "text/plain"
        elif suffix in [".doc", ".docx"]:
            media_type = "application/msword"
        elif suffix in [".xls", ".xlsx"]:
            media_type = "application/vnd.ms-excel"

        # Return the file
        return FileResponse(
            path=str(file_path),
            filename=filename,
            media_type=media_type
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error serving attachment file {connection_id}/{email_id}/{filename}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/attachments/{filename}")
async def serve_attachment_file_legacy(filename: str):
    """
    Legacy endpoint for serving attachment files with simple filename structure.
    This is kept for backward compatibility.

    Args:
        filename: The filename of the attachment file to serve

    Returns:
        FileResponse: The requested file

    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        # Construct the file path (legacy flat structure)
        file_path = Path(settings.PROJECT_ROOT) / "storage" / "attachments" / filename

        # Security check: ensure the file is within the allowed directory
        storage_dir = Path(settings.PROJECT_ROOT) / "storage" / "attachments"
        try:
            file_path.resolve().relative_to(storage_dir.resolve())
        except ValueError:
            logger.warning(f"Attempted access to attachment file outside storage directory: {filename}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Check if file exists
        if not file_path.exists():
            logger.warning(f"Requested attachment file not found: {file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Check if it's actually a file (not a directory)
        if not file_path.is_file():
            logger.warning(f"Requested attachment path is not a file: {file_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        logger.info(f"Serving legacy attachment file: {filename}")

        # Determine media type based on file extension
        media_type = "application/octet-stream"  # Default
        suffix = file_path.suffix.lower()
        if suffix == ".pdf":
            media_type = "application/pdf"
        elif suffix in [".jpg", ".jpeg"]:
            media_type = "image/jpeg"
        elif suffix == ".png":
            media_type = "image/png"
        elif suffix == ".txt":
            media_type = "text/plain"
        elif suffix in [".doc", ".docx"]:
            media_type = "application/msword"
        elif suffix in [".xls", ".xlsx"]:
            media_type = "application/vnd.ms-excel"

        # Return the file
        return FileResponse(
            path=str(file_path),
            filename=filename,
            media_type=media_type
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error serving legacy attachment file {filename}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )