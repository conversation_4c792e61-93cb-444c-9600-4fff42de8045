"""
QuickBooks connection management endpoints
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_db
from app.core.auth import get_current_admin
from app.schemas.quickbooks_connection import (
    QBConnectionCreate, QBConnectionUpdate, QBConnectionResponse,
    QBConnectionStatus, QBTestResult
)
from app.services.quickbooks_database import QuickBooksDatabase
from app.services.quickbooks_oauth_client import QuickBooksOAuthClient

logger = logging.getLogger(__name__)
router = APIRouter()


def get_quickbooks_database(db: AsyncIOMotorDatabase = Depends(get_db)) -> QuickBooksDatabase:
    """Get QuickBooks database service"""
    return QuickBooksDatabase(db)


def get_quickbooks_oauth_client() -> QuickBooksOAuthClient:
    """Get QuickBooks OAuth client"""
    return QuickBooksOAuthClient()


@router.post("/connections", response_model=QBConnectionResponse)
async def create_connection(
    connection_data: QBConnectionCreate,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    qb_db: QuickBooksDatabase = Depends(get_quickbooks_database)
):
    """
    Create a new QuickBooks connection.
    
    This endpoint creates a new QuickBooks connection with encrypted token storage.
    """
    try:
        logger.info(f"Creating QuickBooks connection for realm_id: {connection_data.realm_id}")

        result = await qb_db.create_connection(connection_data)

        if result["success"]:
            return QBConnectionResponse(
                success=True,
                message="QuickBooks connection created successfully",
                connection_status=QBConnectionStatus(
                    is_connected=True,
                    realm_id=connection_data.realm_id,
                    environment=connection_data.environment,
                    token_expires_at=connection_data.token_expires_at,
                    token_status="valid"
                )
            )
        else:
            # Check if the error is because connection already exists
            error_message = result.get("message", "Unknown error")
            if "already exists" in error_message:
                # Connection already exists - return success with existing connection info
                return QBConnectionResponse(
                    success=True,
                    message="QuickBooks connection already exists",
                    connection_status=QBConnectionStatus(
                        is_connected=True,
                        realm_id=connection_data.realm_id,
                        environment=connection_data.environment,
                        token_expires_at=connection_data.token_expires_at,
                        token_status="valid"
                    )
                )
            else:
                # Other error - raise HTTP exception
                raise HTTPException(
                    status_code=400,
                    detail={
                        "success": False,
                        "error": "Connection creation failed",
                        "details": error_message
                    }
                )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error creating QuickBooks connection: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": str(e)
            }
        )


@router.get("/connections")
async def list_connections(
    skip: int = Query(0, ge=0, description="Number of connections to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of connections to return"),
    active_only: bool = Query(True, description="Only return active connections"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    qb_db: QuickBooksDatabase = Depends(get_quickbooks_database)
):
    """
    List all QuickBooks connections with pagination.
    
    This endpoint returns a paginated list of QuickBooks connections
    with their status information and metadata.
    """
    try:
        logger.info(f"Listing QuickBooks connections (skip={skip}, limit={limit}, active_only={active_only})")
        
        result = await qb_db.list_connections(skip=skip, limit=limit, active_only=active_only)
        
        if result["success"]:
            return {
                "success": True,
                "message": f"Retrieved {len(result['connections'])} connections",
                "connections": result["connections"],
                "pagination": {
                    "total": result["total"],
                    "skip": result["skip"],
                    "limit": result["limit"],
                    "has_more": result["skip"] + len(result["connections"]) < result["total"]
                }
            }
        else:
            raise HTTPException(
                status_code=500,
                detail={
                    "success": False,
                    "error": "Failed to retrieve connections",
                    "details": result.get("message", "Unknown error")
                }
            )
            
    except Exception as e:
        logger.error(f"Error listing QuickBooks connections: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": str(e)
            }
        )


@router.get("/connections/{connection_id}")
async def get_connection(
    connection_id: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    qb_db: QuickBooksDatabase = Depends(get_quickbooks_database)
):
    """
    Get a specific QuickBooks connection by ID.
    
    This endpoint returns detailed information about a QuickBooks connection
    without exposing sensitive token data.
    """
    try:
        logger.info(f"Getting QuickBooks connection: {connection_id}")
        
        connection = await qb_db.get_connection(connection_id)
        
        if not connection:
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "error": "Connection not found",
                    "details": f"QuickBooks connection {connection_id} not found"
                }
            )
        
        return {
            "success": True,
            "message": "Connection retrieved successfully",
            "connection": {
                "id": str(connection.id),
                "realm_id": connection.realm_id,
                "client_id": connection.client_id,
                "environment": connection.environment,
                "token_expires_at": connection.token_expires_at,
                "created_at": connection.created_at,
                "updated_at": connection.updated_at,
                "last_tested": connection.last_tested,
                "is_active": connection.is_active,
                "company_info": connection.company_info
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting QuickBooks connection {connection_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": str(e)
            }
        )


@router.put("/connections/{connection_id}")
async def update_connection(
    connection_id: str,
    update_data: QBConnectionUpdate,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    qb_db: QuickBooksDatabase = Depends(get_quickbooks_database)
):
    """
    Update a QuickBooks connection.
    
    This endpoint allows updating connection details including tokens.
    """
    try:
        logger.info(f"Updating QuickBooks connection: {connection_id}")
        
        # Check if connection exists
        connection = await qb_db.get_connection(connection_id)
        if not connection:
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "error": "Connection not found",
                    "details": f"QuickBooks connection {connection_id} not found"
                }
            )
        
        success = await qb_db.update_connection(connection_id, update_data)
        
        if success:
            return {
                "success": True,
                "message": "QuickBooks connection updated successfully",
                "connection_id": connection_id
            }
        else:
            raise HTTPException(
                status_code=400,
                detail={
                    "success": False,
                    "error": "Update failed",
                    "details": "Failed to update QuickBooks connection"
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating QuickBooks connection {connection_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": str(e)
            }
        )


@router.delete("/connections/{connection_id}")
async def delete_connection(
    connection_id: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    qb_db: QuickBooksDatabase = Depends(get_quickbooks_database)
):
    """
    Delete a QuickBooks connection.
    
    This endpoint permanently removes a QuickBooks connection from the database.
    """
    try:
        logger.info(f"Deleting QuickBooks connection: {connection_id}")
        
        # Check if connection exists
        connection = await qb_db.get_connection(connection_id)
        if not connection:
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "error": "Connection not found",
                    "details": f"QuickBooks connection {connection_id} not found"
                }
            )
        
        success = await qb_db.delete_connection(connection_id)
        
        if success:
            return {
                "success": True,
                "message": "QuickBooks connection deleted successfully",
                "connection_id": connection_id
            }
        else:
            raise HTTPException(
                status_code=400,
                detail={
                    "success": False,
                    "error": "Deletion failed",
                    "details": "Failed to delete QuickBooks connection"
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting QuickBooks connection {connection_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": str(e)
            }
        )


@router.post("/connections/{connection_id}/test", response_model=QBTestResult)
async def test_connection(
    connection_id: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    qb_db: QuickBooksDatabase = Depends(get_quickbooks_database),
    oauth_client: QuickBooksOAuthClient = Depends(get_quickbooks_oauth_client)
):
    """
    Test a QuickBooks connection.

    This endpoint tests the connection by validating the access token
    and attempting to refresh it if needed.
    """
    try:
        logger.info(f"Testing QuickBooks connection: {connection_id}")

        # Get connection credentials
        credentials = await qb_db.get_credentials(connection_id)
        if not credentials:
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "error": "Connection not found",
                    "details": f"QuickBooks connection {connection_id} not found"
                }
            )

        # Test token validity
        is_valid = await oauth_client.validate_token(
            credentials.access_token,
            credentials.realm_id
        )

        if is_valid:
            return QBTestResult(
                success=True,
                message="QuickBooks connection is working properly",
                connection_id=connection_id,
                realm_id=credentials.realm_id,
                environment=credentials.environment,
                token_status="valid"
            )
        else:
            # Try to refresh the token
            refresh_result = await oauth_client.refresh_access_token(
                credentials.refresh_token,
                credentials.client_id,
                credentials.client_secret
            )

            if refresh_result.success:
                # Update connection with new tokens
                from app.schemas.quickbooks_connection import QBConnectionUpdate
                update_data = QBConnectionUpdate(
                    access_token=refresh_result.new_access_token,
                    refresh_token=refresh_result.new_refresh_token,
                    token_expires_at=refresh_result.new_expires_at
                )

                await qb_db.update_connection(connection_id, update_data)

                return QBTestResult(
                    success=True,
                    message="QuickBooks connection refreshed and working",
                    connection_id=connection_id,
                    realm_id=credentials.realm_id,
                    environment=credentials.environment,
                    token_status="refreshed"
                )
            else:
                return QBTestResult(
                    success=False,
                    message=f"QuickBooks connection failed: {refresh_result.message}",
                    connection_id=connection_id,
                    realm_id=credentials.realm_id,
                    environment=credentials.environment,
                    token_status="invalid",
                    error_details=refresh_result.error_code
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing QuickBooks connection {connection_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": str(e)
            }
        )
