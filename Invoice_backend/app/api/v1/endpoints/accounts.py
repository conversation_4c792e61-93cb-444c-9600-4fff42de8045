"""
Account management endpoints
"""

import logging
from fastapi import APIRouter, HTTPException, Depends
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_db
from app.schemas.account import AccountListResponse
from app.services.quickbooks_service import create_quickbooks_service_legacy

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=AccountListResponse)
async def get_accounts(
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Fetch all accounting heads (chart of accounts) from QuickBooks.
    
    This endpoint retrieves all account categories and their details from QuickBooks,
    including account types, names, and IDs that can be used for bill line items.
    
    Returns:
        JSON response with list of accounts or error information
        
    Raises:
        HTTPException: For authentication failures or API errors
    """
    try:
        # Initialize QuickBooks service (legacy mode for backward compatibility)
        qb_service = create_quickbooks_service_legacy()
        
        # Log the incoming request
        logger.info("Received request to fetch all accounts from QuickBooks")
        
        # Validate QuickBooks authentication
        try:
            if not await qb_service.authenticate():
                logger.error("QuickBooks authentication failed")
                raise HTTPException(
                    status_code=401,
                    detail={
                        "success": False,
                        "error": "Authentication failed",
                        "details": "Unable to authenticate with QuickBooks API. Please check credentials."
                    }
                )
        except ValueError as e:
            # Handle missing access token
            logger.error(f"QuickBooks configuration error: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "success": False,
                    "error": "Configuration error",
                    "details": "QuickBooks API credentials not properly configured."
                }
            )
        
        # Fetch accounts from QuickBooks
        result = await qb_service.get_accounts()
        
        # Handle the result
        if result["success"]:
            logger.info(f"Successfully retrieved {result['total_accounts']} account names")
            return AccountListResponse(**result)
        else:
            # QuickBooks API returned an error
            status_code = result.get("status_code", 500)
            if status_code == 401:
                status_code = 401
            elif status_code >= 400 and status_code < 500:
                status_code = 400
            else:
                status_code = 500
                
            logger.error(f"QuickBooks API error: {result['error']}")
            raise HTTPException(
                status_code=status_code,
                detail=result
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
        
    except Exception as e:
        # Handle unexpected errors
        error_msg = f"Unexpected error fetching accounts: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Internal server error",
                "details": error_msg
            }
        )