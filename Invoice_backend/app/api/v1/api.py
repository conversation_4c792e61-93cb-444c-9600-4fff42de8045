"""
Main API router that includes all endpoint routers
"""

from fastapi import APIRouter
from app.api.v1.endpoints import bills, accounts, health, attachments, invoice_extraction, admin, invoice_upload
from app.api.v1.endpoints import outlook_original as outlook
from app.api.v1.endpoints import files, quickbooks

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(bills.router, prefix="/bills", tags=["bills"])
api_router.include_router(accounts.router, prefix="/accounts", tags=["accounts"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
api_router.include_router(outlook.router, prefix="/outlook", tags=["outlook"])
api_router.include_router(quickbooks.router, prefix="/quickbooks", tags=["quickbooks"])
api_router.include_router(attachments.router, prefix="/attachments", tags=["attachments"])
api_router.include_router(invoice_extraction.router, prefix="/invoices", tags=["invoices"])
api_router.include_router(invoice_upload.router, prefix="/invoices", tags=["invoice-upload"])
api_router.include_router(files.router, prefix="/files", tags=["files"])
