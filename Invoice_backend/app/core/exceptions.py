"""
Custom exception handlers for the application
"""

import uuid
import logging
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

logger = logging.getLogger(__name__)

def add_exception_handlers(app: FastAPI):
    """Add custom exception handlers to FastAPI app"""
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """
        Custom handler for Pydantic validation errors to provide detailed field-level messages
        """
        correlation_id = str(uuid.uuid4())
        logger.error(f"Validation error [ID: {correlation_id}]: {exc.errors()}")
        
        # Format validation errors for better user experience
        formatted_errors = []
        for error in exc.errors():
            field_path = " -> ".join(str(loc) for loc in error["loc"])
            formatted_errors.append({
                "field": field_path,
                "message": error["msg"],
                "invalid_value": error.get("input", "N/A")
            })
        
        return JSONResponse(
            status_code=422,
            content={
                "success": False,
                "error": "Validation failed",
                "details": "One or more fields contain invalid data",
                "validation_errors": formatted_errors,
                "correlation_id": correlation_id
            }
        )

    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request: Request, exc: StarletteHTTPException):
        """
        Custom handler for HTTP exceptions
        """
        correlation_id = str(uuid.uuid4())
        logger.error(f"HTTP error [ID: {correlation_id}]: {exc.status_code} - {exc.detail}")
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": f"HTTP {exc.status_code}",
                "details": exc.detail,
                "correlation_id": correlation_id
            }
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """
        Global exception handler for unexpected errors.
        Note: HTTPExceptions are handled by specific handlers, so we skip them here.
        """
        # Skip HTTPExceptions as they should be handled by specific handlers
        if isinstance(exc, StarletteHTTPException):
            raise exc

        correlation_id = str(uuid.uuid4())
        error_msg = f"Unexpected server error [ID: {correlation_id}]: {str(exc)}"
        logger.error(error_msg, exc_info=True)

        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": "Internal server error",
                "details": "An unexpected error occurred while processing your request",
                "correlation_id": correlation_id
            }
        )