"""
Application configuration settings
"""

import os
from pathlib import Path
from typing import List, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Project Information
    PROJECT_NAME: str = "QuickBooks Bill API"
    PROJECT_DESCRIPTION: str = "API for creating and managing bills in QuickBooks with MongoDB storage"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    PROJECT_ROOT: str = str(Path(__file__).parent.parent.parent)
    
    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    ENVIRONMENT: str = "development"
    
    # CORS Configuration
    BACKEND_CORS_ORIGINS: List[str] = ["*"]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # MongoDB Configuration
    MONGODB_URL: str = "mongodb://localhost:27017"
    DATABASE_NAME: str = "quickbooks_bills"
    
    # Collections
    BILLS_COLLECTION: str = "bills"
    VENDORS_COLLECTION: str = "vendors"
    ACCOUNTS_COLLECTION: str = "accounts"
    OUTLOOK_CONNECTIONS_COLLECTION: str = "outlook_connections"
    QUICKBOOKS_CONNECTIONS_COLLECTION: str = "quickbooks_connections"
    EMAIL_TRACKING_COLLECTION: str = "email_tracking"
    INVOICE_JOBS_COLLECTION: str = "invoice_jobs"
    INVOICES_COLLECTION: str = "invoices"
    
    # QuickBooks Configuration
    QB_REALM_ID: str = "****************"
    QB_BASE_URL: str = f"https://sandbox-quickbooks.api.intuit.com/v3/company/{QB_REALM_ID}"
    QB_ACCESS_TOKEN: str = Field(..., env="QB_ACCESS_TOKEN")
    QB_CLIENT_ID: str = Field(..., env="QB_CLIENT_ID")
    QB_CLIENT_SECRET: str = Field(..., env="QB_CLIENT_SECRET")
    
    # Microsoft Graph API Configuration
    MICROSOFT_CLIENT_ID: str = Field(..., env="MICROSOFT_CLIENT_ID")
    MICROSOFT_CLIENT_SECRET: str = Field(..., env="MICROSOFT_CLIENT_SECRET")
    MICROSOFT_TENANT_ID: str = Field(default="common", env="MICROSOFT_TENANT_ID")
    MICROSOFT_REDIRECT_URI: str = Field(default="http://localhost:8000/api/v1/outlook/auth/callback", env="MICROSOFT_REDIRECT_URI")
    MICROSOFT_SCOPES: List[str] = ["https://graph.microsoft.com/Mail.Read", "https://graph.microsoft.com/User.Read"]

    # Email Processing Configuration
    EMAIL_FETCH_BATCH_SIZE: int = Field(..., env="EMAIL_FETCH_BATCH_SIZE")
    EMAIL_RETENTION_DAYS: int = Field(default=90, env="EMAIL_RETENTION_DAYS")
    AUTO_SYNC_INTERVAL_MINUTES: int = Field(default=30, env="AUTO_SYNC_INTERVAL_MINUTES")

    # Database Collections for Outlook
    OUTLOOK_CONNECTIONS_COLLECTION: str = Field(default="outlook_connections", env="OUTLOOK_CONNECTIONS_COLLECTION")
    EMAIL_TRACKING_COLLECTION: str = Field(default="email_tracking", env="EMAIL_TRACKING_COLLECTION")
    
    # Security
    SECRET_KEY: str = "your-secret-key-here"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": True
    }


settings = Settings()