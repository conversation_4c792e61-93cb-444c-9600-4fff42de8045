"""
Error handling middleware and exception handlers.
Provides structured error responses with correlation IDs.
"""

import logging
import traceback
import uuid
from typing import Dict, Any, Optional

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.schemas.outlook import OutlookErrorResponse

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for handling errors and adding correlation IDs."""

    async def dispatch(self, request: Request, call_next):
        # Generate correlation ID for this request
        correlation_id = str(uuid.uuid4())
        request.state.correlation_id = correlation_id

        try:
            response = await call_next(request)
            response.headers["X-Correlation-ID"] = correlation_id
            return response

        except HTTPException:
            # Let HTTPExceptions pass through to be handled by the proper exception handlers
            raise
        except Exception as exc:
            logger.error(
                f"Unhandled exception in request {request.url}",
                extra={
                    'correlation_id': correlation_id,
                    'method': request.method,
                    'url': str(request.url),
                    'exception': str(exc),
                    'traceback': traceback.format_exc()
                }
            )

            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "success": False,
                    "error_code": "INTERNAL_SERVER_ERROR",
                    "error_message": "An internal server error occurred",
                    "correlation_id": correlation_id,
                    "timestamp": None  # Will be set by schema
                },
                headers={"X-Correlation-ID": correlation_id}
            )


def create_error_response(
    error_code: str,
    error_message: str,
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    details: Optional[Dict[str, Any]] = None,
    correlation_id: Optional[str] = None
) -> JSONResponse:
    """
    Create a structured error response.

    Args:
        error_code: Error code identifier
        error_message: Human-readable error message
        status_code: HTTP status code
        details: Additional error details
        correlation_id: Request correlation ID

    Returns:
        JSONResponse with structured error
    """
    error_response = OutlookErrorResponse(
        error_code=error_code,
        error_message=error_message,
        details=details
    )

    response_data = error_response.model_dump()
    if correlation_id:
        response_data["correlation_id"] = correlation_id

    return JSONResponse(
        status_code=status_code,
        content=response_data,
        headers={"X-Correlation-ID": correlation_id} if correlation_id else {}
    )


async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions with structured responses."""
    correlation_id = getattr(request.state, 'correlation_id', str(uuid.uuid4()))

    # Map HTTP status codes to error codes
    error_code_map = {
        400: "BAD_REQUEST",
        401: "UNAUTHORIZED",
        403: "FORBIDDEN",
        404: "NOT_FOUND",
        409: "CONFLICT",
        422: "VALIDATION_ERROR",
        429: "RATE_LIMITED",
        500: "INTERNAL_SERVER_ERROR",
        502: "BAD_GATEWAY",
        503: "SERVICE_UNAVAILABLE",
        504: "GATEWAY_TIMEOUT"
    }

    error_code = error_code_map.get(exc.status_code, "HTTP_ERROR")

    logger.warning(
        f"HTTP exception: {exc.status_code} - {exc.detail}",
        extra={
            'correlation_id': correlation_id,
            'status_code': exc.status_code,
            'detail': exc.detail,
            'url': str(request.url),
            'method': request.method
        }
    )

    return create_error_response(
        error_code=error_code,
        error_message=str(exc.detail),
        status_code=exc.status_code,
        correlation_id=correlation_id
    )


async def validation_exception_handler(request: Request, exc):
    """Handle validation exceptions."""
    correlation_id = getattr(request.state, 'correlation_id', str(uuid.uuid4()))

    # Extract validation errors
    errors = []
    for error in exc.errors():
        errors.append({
            "field": ".".join(str(x) for x in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })

    logger.warning(
        f"Validation error: {errors}",
        extra={
            'correlation_id': correlation_id,
            'validation_errors': errors,
            'url': str(request.url),
            'method': request.method
        }
    )

    return create_error_response(
        error_code="VALIDATION_ERROR",
        error_message="Request validation failed",
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        details={"validation_errors": errors},
        correlation_id=correlation_id
    )


class OutlookAPIException(Exception):
    """Custom exception for Outlook API errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "OUTLOOK_API_ERROR",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details
        super().__init__(message)


async def outlook_api_exception_handler(request: Request, exc: OutlookAPIException):
    """Handle custom Outlook API exceptions."""
    correlation_id = getattr(request.state, 'correlation_id', str(uuid.uuid4()))

    logger.error(
        f"Outlook API error: {exc.message}",
        extra={
            'correlation_id': correlation_id,
            'error_code': exc.error_code,
            'details': exc.details,
            'url': str(request.url),
            'method': request.method
        }
    )

    return create_error_response(
        error_code=exc.error_code,
        error_message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        correlation_id=correlation_id
    )