"""
Bill management service
"""

import logging
from typing import List, Optional, Dict
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId

from app.core.config import settings
from app.schemas.bill import Bill<PERSON><PERSON>, BillInDB, BillResponse, BillCreateResponse
from app.services.quickbooks_service import QuickBooksService, create_quickbooks_service_legacy

logger = logging.getLogger(__name__)


class BillService:
    """Service for managing bills"""

    def __init__(self, db: AsyncIOMotorDatabase, connection_id: Optional[str] = None):
        self.db = db
        self.collection = db[settings.BILLS_COLLECTION]

        # Use database-backed QuickBooks service if connection_id provided, otherwise legacy mode
        if connection_id:
            self.qb_service = QuickBooksService(db=db, connection_id=connection_id)
        else:
            self.qb_service = create_quickbooks_service_legacy()
    
    async def create_bill(self, bill_data: BillCreate) -> BillCreateResponse:
        """Create a new bill and sync with <PERSON>B<PERSON>s"""
        try:
            # Create bill document for database
            bill_doc = BillInDB(
                vendor=bill_data.vendor,
                invoice_number=bill_data.invoice_number,
                invoice_date=bill_data.invoice_date,
                due_date=bill_data.due_date,
                total_amount=bill_data.total_amount,
                line_items=bill_data.line_items,
                memo=bill_data.memo,
                status="pending"
            )
            
            # Insert bill into database
            result = await self.collection.insert_one(bill_doc.model_dump(by_alias=True, exclude={"id"}))
            bill_id = str(result.inserted_id)
            
            logger.info(f"Bill saved to database with ID: {bill_id}")
            
            # Try to create bill in QuickBooks
            try:
                qb_result = await self.qb_service.create_bill(bill_data)
                
                if qb_result["success"]:
                    # Update bill with QuickBooks information
                    await self.collection.update_one(
                        {"_id": ObjectId(bill_id)},
                        {
                            "$set": {
                                "quickbooks_bill_id": qb_result["quickbooks_bill_id"],
                                "quickbooks_vendor_id": qb_result.get("quickbooks_vendor_id"),
                                "status": "created",
                                "updated_at": datetime.utcnow()
                            }
                        }
                    )
                    
                    logger.info(f"Bill {bill_id} successfully created in QuickBooks")
                    
                    return BillCreateResponse(
                        success=True,
                        message="Bill created successfully in QuickBooks and database",
                        bill_id=bill_id,
                        quickbooks_bill_id=qb_result["quickbooks_bill_id"],
                        bill_details=qb_result["bill_details"]
                    )
                else:
                    # Update bill status to failed
                    await self.collection.update_one(
                        {"_id": ObjectId(bill_id)},
                        {
                            "$set": {
                                "status": "failed",
                                "error_details": qb_result,
                                "updated_at": datetime.utcnow()
                            }
                        }
                    )
                    
                    logger.error(f"Failed to create bill {bill_id} in QuickBooks: {qb_result}")
                    
                    return BillCreateResponse(
                        success=False,
                        message=f"Bill saved to database but failed to create in QuickBooks: {qb_result.get('error', 'Unknown error')}",
                        bill_id=bill_id
                    )
                    
            except Exception as qb_error:
                # Update bill status to failed
                await self.collection.update_one(
                    {"_id": ObjectId(bill_id)},
                    {
                        "$set": {
                            "status": "failed",
                            "error_details": {"error": str(qb_error)},
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                
                logger.error(f"Exception creating bill {bill_id} in QuickBooks: {str(qb_error)}")
                
                return BillCreateResponse(
                    success=False,
                    message=f"Bill saved to database but QuickBooks integration failed: {str(qb_error)}",
                    bill_id=bill_id
                )
                
        except Exception as e:
            logger.error(f"Error creating bill: {str(e)}")
            return BillCreateResponse(
                success=False,
                message=f"Failed to create bill: {str(e)}"
            )
    
    async def get_bill_by_id(self, bill_id: str) -> Optional[BillResponse]:
        """Get a bill by its ID"""
        try:
            bill_doc = await self.collection.find_one({"_id": ObjectId(bill_id)})
            if bill_doc:
                bill_doc["id"] = str(bill_doc["_id"])
                return BillResponse(**bill_doc)
            return None
        except Exception as e:
            logger.error(f"Error getting bill {bill_id}: {str(e)}")
            return None
    
    async def get_bills(
        self, 
        skip: int = 0, 
        limit: int = 10, 
        vendor_name: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict:
        """Get paginated list of bills with optional filters"""
        try:
            # Build filter query
            filter_query = {}
            if vendor_name:
                filter_query["vendor.name"] = {"$regex": vendor_name, "$options": "i"}
            if status:
                filter_query["status"] = status
            
            # Get total count
            total = await self.collection.count_documents(filter_query)
            
            # Get bills with pagination
            cursor = self.collection.find(filter_query).sort("created_at", -1).skip(skip).limit(limit)
            bills = []
            
            async for bill_doc in cursor:
                bill_doc["id"] = str(bill_doc["_id"])
                bills.append(BillResponse(**bill_doc))
            
            return {
                "bills": bills,
                "total": total,
                "page": (skip // limit) + 1,
                "size": limit,
                "pages": (total + limit - 1) // limit
            }
            
        except Exception as e:
            logger.error(f"Error getting bills: {str(e)}")
            return {
                "bills": [],
                "total": 0,
                "page": 1,
                "size": limit,
                "pages": 0
            }
    
    async def update_bill_status(self, bill_id: str, status: str, details: Optional[Dict] = None) -> bool:
        """Update bill status"""
        try:
            update_data = {
                "status": status,
                "updated_at": datetime.utcnow()
            }
            
            if details:
                update_data.update(details)
            
            result = await self.collection.update_one(
                {"_id": ObjectId(bill_id)},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating bill {bill_id} status: {str(e)}")
            return False
    
    async def delete_bill(self, bill_id: str) -> bool:
        """Delete a bill"""
        try:
            result = await self.collection.delete_one({"_id": ObjectId(bill_id)})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error deleting bill {bill_id}: {str(e)}")
            return False