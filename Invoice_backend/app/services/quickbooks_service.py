"""
QuickBooks API integration service with automatic token refresh
"""

import logging
import requests
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import settings
from app.schemas.bill import BillCreate
from app.schemas.quickbooks_connection import QBCredentials, QBConnectionUpdate
from app.services.quickbooks_database import QuickBooksDatabase
from app.services.quickbooks_oauth_client import QuickBooksOAuthClient
from app.services.token_manager import token_manager

logger = logging.getLogger(__name__)


class QuickBooksService:
    """Service for communicating with QuickBooks API with automatic token refresh"""

    def __init__(self, db: Optional[AsyncIOMotorDatabase] = None, connection_id: Optional[str] = None):
        """
        Initialize QuickBooks service with configuration.

        Args:
            db: Database instance for connection management (optional)
            connection_id: Specific connection ID to use (optional)

        If db and connection_id are provided, uses database-backed connection management.
        Otherwise, falls back to static configuration from settings for backward compatibility.
        """
        self.db = db
        self.connection_id = connection_id
        self.use_database = db is not None and connection_id is not None

        # Initialize database services if using database mode
        if self.use_database:
            self.qb_db = QuickBooksDatabase(db)
            self.oauth_client = QuickBooksOAuthClient()
            self.current_credentials: Optional[QBCredentials] = None
        else:
            # Legacy mode - use static configuration
            self.realm_id = settings.QB_REALM_ID
            self.base_url = settings.QB_BASE_URL
            self.access_token = settings.QB_ACCESS_TOKEN
            self.client_id = settings.QB_CLIENT_ID
            self.client_secret = settings.QB_CLIENT_SECRET

        # HTTP session for reusing connections
        self.session = requests.Session()

    async def _ensure_valid_credentials(self) -> QBCredentials:
        """
        Ensure we have valid credentials, refreshing tokens if necessary.

        Returns:
            Valid credentials

        Raises:
            ValueError: If credentials cannot be obtained or refreshed
        """
        if not self.use_database:
            # Legacy mode - return static credentials
            if not all([self.realm_id, self.access_token, self.client_id, self.client_secret]):
                raise ValueError("QuickBooks credentials not properly configured")

            return QBCredentials(
                realm_id=self.realm_id,
                client_id=self.client_id,
                client_secret=self.client_secret,
                access_token=self.access_token,
                refresh_token="",  # Not available in legacy mode
                token_expires_at=datetime.now(timezone.utc) + timedelta(hours=1),  # Assume valid
                environment="sandbox"
            )

        # Database mode - get credentials and refresh if needed
        credentials = await self.qb_db.get_credentials(self.connection_id)
        if not credentials:
            raise ValueError(f"QuickBooks connection {self.connection_id} not found")

        # Check if token needs refresh (5-minute buffer)
        now = datetime.now(timezone.utc)
        buffer_time = now + timedelta(minutes=5)

        if credentials.token_expires_at <= buffer_time:
            logger.info(f"QuickBooks token for connection {self.connection_id} needs refresh")

            # Attempt token refresh
            refresh_result = await self.oauth_client.refresh_access_token(
                credentials.refresh_token,
                credentials.client_id,
                credentials.client_secret
            )

            if refresh_result.success:
                # Update database with new tokens
                update_data = QBConnectionUpdate(
                    access_token=refresh_result.new_access_token,
                    refresh_token=refresh_result.new_refresh_token,
                    token_expires_at=refresh_result.new_expires_at
                )

                success = await self.qb_db.update_connection(self.connection_id, update_data)
                if success:
                    logger.info(f"Successfully refreshed QuickBooks token for connection {self.connection_id}")
                    # Update credentials with new tokens
                    credentials.access_token = refresh_result.new_access_token
                    credentials.refresh_token = refresh_result.new_refresh_token
                    credentials.token_expires_at = refresh_result.new_expires_at
                else:
                    logger.error(f"Failed to update database with refreshed tokens for connection {self.connection_id}")
                    raise ValueError("Failed to update refreshed tokens in database")
            else:
                logger.error(f"Failed to refresh QuickBooks token for connection {self.connection_id}: {refresh_result.message}")
                raise ValueError(f"Token refresh failed: {refresh_result.message}")

        self.current_credentials = credentials
        return credentials

    async def _get_headers(self) -> Dict[str, str]:
        """Get HTTP headers for QuickBooks API requests with automatic token refresh"""
        credentials = await self._ensure_valid_credentials()

        return {
            "Authorization": f"Bearer {credentials.access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

    def _get_base_url(self) -> str:
        """Get the base URL for QuickBooks API requests"""
        if self.use_database and self.current_credentials:
            environment = self.current_credentials.environment
            realm_id = self.current_credentials.realm_id
            if environment == "production":
                return f"https://quickbooks.api.intuit.com/v3/company/{realm_id}"
            else:
                return f"https://sandbox-quickbooks.api.intuit.com/v3/company/{realm_id}"
        else:
            # Legacy mode
            return self.base_url

    def _get_realm_id(self) -> str:
        """Get the realm ID for QuickBooks API requests"""
        if self.use_database and self.current_credentials:
            return self.current_credentials.realm_id
        else:
            return self.realm_id
    
    async def authenticate(self) -> bool:
        """Verify authentication with QuickBooks API with automatic token refresh"""
        try:
            headers = await self._get_headers()
            base_url = self._get_base_url()
            realm_id = self._get_realm_id()

            # Test authentication with a simple company info request
            response = self.session.get(
                f"{base_url}/companyinfo/{realm_id}",
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                logger.info("QuickBooks authentication successful")
                return True
            else:
                logger.error(f"QuickBooks authentication failed: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"QuickBooks authentication error: {str(e)}")
            return False
    
    async def create_or_find_vendor(self, vendor_name: str, vendor_address: Optional[str] = None) -> str:
        """Create a new vendor or find existing one by name with automatic token refresh"""
        try:
            # First, try to find existing vendor by name
            headers = await self._get_headers()
            base_url = self._get_base_url()
            search_query = f"SELECT * FROM Vendor WHERE DisplayName = '{vendor_name}'"

            response = self.session.get(
                f"{base_url}/query?query={search_query}",
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if "QueryResponse" in data and "Vendor" in data["QueryResponse"]:
                    vendors = data["QueryResponse"]["Vendor"]
                    if vendors:
                        vendor_id = vendors[0].get('Id')
                        logger.info(f"Found existing vendor '{vendor_name}' with ID: {vendor_id}")
                        return vendor_id
            
            # If vendor doesn't exist, create a new one
            logger.info(f"Creating new vendor: {vendor_name}")
            vendor_data = {
                "DisplayName": vendor_name
            }
            
            if vendor_address:
                vendor_data["BillAddr"] = {
                    "Line1": vendor_address
                }
            
            response = self.session.post(
                f"{base_url}/vendor",
                headers=headers,
                json=vendor_data,
                timeout=30
            )
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"Vendor creation response: {response_data}")
                
                if "QueryResponse" in response_data and "Vendor" in response_data["QueryResponse"]:
                    new_vendor = response_data["QueryResponse"]["Vendor"][0]
                    vendor_id = new_vendor.get('Id')
                    logger.info(f"Created new vendor '{vendor_name}' with ID: {vendor_id}")
                    return vendor_id
                else:
                    logger.error(f"Unexpected vendor creation response format: {response_data}")
                    return "56"  # Fallback vendor ID
            else:
                logger.error(f"Failed to create vendor: {response.status_code} - {response.text}")
                return "56"  # Fallback vendor ID
                
        except Exception as e:
            logger.error(f"Error creating/finding vendor: {str(e)}")
            return "56"  # Fallback vendor ID

    async def format_bill_for_qb(self, bill_data: BillCreate, vendor_id: str) -> Dict:
        """Format bill data for QuickBooks API schema"""
        
        # Create line items for QuickBooks format
        qb_lines = []
        
        if bill_data.line_items:
            # Use detailed line items
            for item in bill_data.line_items:
                line = {
                    "Amount": item.amount * item.quantity,
                    "DetailType": "AccountBasedExpenseLineDetail",
                    "AccountBasedExpenseLineDetail": {
                        "AccountRef": {
                            "value": "1"  # Use a valid account ID
                        }
                    }
                }
                # Add description if provided
                if item.description:
                    line["Description"] = item.description
                qb_lines.append(line)
        else:
            # Create single line item for total amount
            line = {
                "Amount": bill_data.total_amount,
                "DetailType": "AccountBasedExpenseLineDetail",
                "AccountBasedExpenseLineDetail": {
                    "AccountRef": {
                        "value": "1"  # Use a valid account ID
                    }
                },
                "Description": f"Bill from {bill_data.vendor.name}"
            }
            qb_lines.append(line)
        
        # Format bill for QuickBooks API
        qb_bill = {
            "VendorRef": {
                "value": vendor_id,
                "name": bill_data.vendor.name
            },
            "TxnDate": bill_data.invoice_date,
            "Line": qb_lines
        }
        
        # Add optional fields
        if bill_data.due_date:
            qb_bill["DueDate"] = bill_data.due_date
            
        if bill_data.invoice_number:
            qb_bill["DocNumber"] = bill_data.invoice_number
            
        if bill_data.memo:
            qb_bill["PrivateNote"] = bill_data.memo
            
        return qb_bill
    
    async def create_bill(self, bill_data: BillCreate) -> Dict:
        """Create a bill in QuickBooks and return the result"""
        try:
            # Create or find the vendor first
            vendor_id = await self.create_or_find_vendor(bill_data.vendor.name, bill_data.vendor.address)
            
            # Format bill data for QuickBooks
            qb_bill_data = await self.format_bill_for_qb(bill_data, vendor_id)
            
            # Get headers for API request
            headers = await self._get_headers()
            base_url = self._get_base_url()

            # Make API request to create bill
            logger.info(f"Creating bill for vendor: {bill_data.vendor.name}")
            response = self.session.post(
                f"{base_url}/bill",
                headers=headers,
                json=qb_bill_data,
                timeout=30
            )
            
            # Handle response
            if response.status_code == 200:
                response_data = response.json()
                
                # Extract bill information from QuickBooks response
                if "QueryResponse" in response_data and "Bill" in response_data["QueryResponse"]:
                    qb_bill = response_data["QueryResponse"]["Bill"][0]
                elif "Bill" in response_data:
                    qb_bill = response_data["Bill"]
                else:
                    qb_bill = response_data
                
                logger.info(f"Bill created successfully with ID: {qb_bill.get('Id', 'Unknown')}")
                
                return {
                    "success": True,
                    "message": "Bill created successfully",
                    "quickbooks_bill_id": qb_bill.get("Id"),
                    "quickbooks_vendor_id": vendor_id,
                    "bill_details": {
                        "id": qb_bill.get("Id"),
                        "vendor_name": bill_data.vendor.name,
                        "total_amount": bill_data.total_amount,
                        "created_date": qb_bill.get("MetaData", {}).get("CreateTime"),
                        "doc_number": qb_bill.get("DocNumber")
                    }
                }
            else:
                error_msg = f"QuickBooks API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                
                return {
                    "success": False,
                    "error": "QuickBooks API error",
                    "details": response.text,
                    "status_code": response.status_code
                }
                
        except requests.exceptions.Timeout:
            error_msg = "QuickBooks API request timed out"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "Request timeout",
                "details": error_msg
            }
            
        except requests.exceptions.RequestException as e:
            error_msg = f"QuickBooks API request failed: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "API request failed",
                "details": error_msg
            }
            
        except Exception as e:
            error_msg = f"Unexpected error creating bill: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "Internal error",
                "details": error_msg
            }
    
    async def get_accounts(self) -> Dict:
        """Fetch all accounting heads (chart of accounts) from QuickBooks with automatic token refresh"""
        try:
            # Get headers for API request
            headers = await self._get_headers()
            base_url = self._get_base_url()

            # Query for all accounts from QuickBooks
            logger.info("Fetching all accounts from QuickBooks")
            response = self.session.get(
                f"{base_url}/query?query=SELECT * FROM Account",
                headers=headers,
                timeout=30
            )
            
            # Handle response
            if response.status_code == 200:
                response_data = response.json()
                
                # Extract accounts information from QuickBooks response
                if "QueryResponse" in response_data and "Account" in response_data["QueryResponse"]:
                    accounts = response_data["QueryResponse"]["Account"]
                    
                    # Extract only account names and sort them
                    account_names = []
                    account_details = []
                    
                    for account in accounts:
                        account_name = account.get("Name")
                        if account_name and account.get("Active", True):
                            account_names.append(account_name)
                            account_details.append({
                                "id": account.get("Id"),
                                "name": account_name,
                                "account_type": account.get("AccountType", ""),
                                "quickbooks_account_id": account.get("Id"),
                                "active": account.get("Active", True)
                            })
                    
                    # Sort account names alphabetically
                    account_names.sort()
                    
                    logger.info(f"Successfully retrieved {len(account_names)} account names")
                    
                    return {
                        "success": True,
                        "message": f"Retrieved {len(account_names)} account names successfully",
                        "total_accounts": len(account_names),
                        "account_names": account_names,
                        "accounts": account_details
                    }
                else:
                    logger.warning("No accounts found in QuickBooks response")
                    return {
                        "success": True,
                        "message": "No accounts found",
                        "total_accounts": 0,
                        "account_names": [],
                        "accounts": []
                    }
            else:
                error_msg = f"QuickBooks API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                
                return {
                    "success": False,
                    "error": "QuickBooks API error",
                    "details": response.text,
                    "status_code": response.status_code
                }
                
        except requests.exceptions.Timeout:
            error_msg = "QuickBooks API request timed out"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "Request timeout",
                "details": error_msg
            }
            
        except requests.exceptions.RequestException as e:
            error_msg = f"QuickBooks API request failed: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "API request failed",
                "details": error_msg
            }
            
        except Exception as e:
            error_msg = f"Unexpected error fetching accounts: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "Internal error",
                "details": error_msg
            }


# Factory functions for creating QuickBooksService instances
def create_quickbooks_service_legacy() -> QuickBooksService:
    """
    Create a QuickBooksService instance using legacy static configuration.

    Returns:
        QuickBooksService instance configured with static settings
    """
    return QuickBooksService()


def create_quickbooks_service_with_connection(
    db: AsyncIOMotorDatabase,
    connection_id: str
) -> QuickBooksService:
    """
    Create a QuickBooksService instance using database connection management.

    Args:
        db: Database instance
        connection_id: QuickBooks connection ID

    Returns:
        QuickBooksService instance configured with database connection
    """
    return QuickBooksService(db=db, connection_id=connection_id)