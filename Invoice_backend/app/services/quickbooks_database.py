"""
QuickBooks connection database operations.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import settings
from app.schemas.quickbooks_connection import (
    QBConnectionCreate, QBConnectionUpdate, QBConnectionInDB, QBCredentials
)
from app.services.token_manager import token_manager

logger = logging.getLogger(__name__)


class QuickBooksDatabase:
    """Database operations for QuickBooks connections"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        """
        Initialize QuickBooks database operations.
        
        Args:
            db: MongoDB database instance
        """
        self.db = db
        self.connections_collection = db[settings.QUICKBOOKS_CONNECTIONS_COLLECTION]
    
    async def create_connection(self, connection_data: QBConnectionCreate) -> Dict[str, Any]:
        """
        Create a new QuickBooks connection.
        
        Args:
            connection_data: Connection data to create
            
        Returns:
            Result with success status and connection ID if successful
        """
        try:
            # Check if connection already exists for this realm_id
            existing = await self.connections_collection.find_one({
                "realm_id": connection_data.realm_id
            })
            
            if existing:
                logger.info(f"Connection already exists for realm_id {connection_data.realm_id}")
                return {
                    "success": False,
                    "message": f"Connection already exists for realm_id {connection_data.realm_id}",
                    "connection_id": str(existing["_id"])
                }
            
            # Encrypt sensitive data
            access_token_encrypted = token_manager.encryption.encrypt_token(
                connection_data.access_token
            )
            refresh_token_encrypted = token_manager.encryption.encrypt_token(
                connection_data.refresh_token
            )
            client_secret_encrypted = token_manager.encryption.encrypt_token(
                connection_data.client_secret
            )
            
            # Create connection document
            now = datetime.now(timezone.utc)
            connection = {
                "realm_id": connection_data.realm_id,
                "client_id": connection_data.client_id,
                "client_secret_encrypted": client_secret_encrypted,
                "access_token_encrypted": access_token_encrypted,
                "refresh_token_encrypted": refresh_token_encrypted,
                "token_expires_at": connection_data.token_expires_at,
                "environment": connection_data.environment,
                "created_at": now,
                "updated_at": now,
                "last_tested": None,
                "is_active": True,
                "company_info": None
            }
            
            # Insert into database
            result = await self.connections_collection.insert_one(connection)
            
            logger.info(f"Created new QuickBooks connection for realm_id {connection_data.realm_id}")
            return {
                "success": True,
                "connection_id": str(result.inserted_id)
            }
            
        except Exception as e:
            logger.error(f"Error creating QuickBooks connection: {str(e)}")
            return {
                "success": False,
                "error": "database_error",
                "message": str(e)
            }
    
    async def get_connection(self, connection_id: str) -> Optional[QBConnectionInDB]:
        """
        Get a QuickBooks connection by ID.
        
        Args:
            connection_id: Connection ID
            
        Returns:
            Connection data if found, None otherwise
        """
        try:
            connection = await self.connections_collection.find_one({
                "_id": ObjectId(connection_id)
            })
            
            if connection:
                return QBConnectionInDB(**connection)
            return None
            
        except Exception as e:
            logger.error(f"Error getting QuickBooks connection {connection_id}: {str(e)}")
            return None
    
    async def get_connection_by_realm_id(self, realm_id: str) -> Optional[QBConnectionInDB]:
        """
        Get a QuickBooks connection by realm ID.
        
        Args:
            realm_id: QuickBooks realm ID
            
        Returns:
            Connection data if found, None otherwise
        """
        try:
            connection = await self.connections_collection.find_one({
                "realm_id": realm_id
            })
            
            if connection:
                return QBConnectionInDB(**connection)
            return None
            
        except Exception as e:
            logger.error(f"Error getting QuickBooks connection by realm_id {realm_id}: {str(e)}")
            return None
    
    async def update_connection(
        self, 
        connection_id: str, 
        update_data: QBConnectionUpdate
    ) -> bool:
        """
        Update a QuickBooks connection.
        
        Args:
            connection_id: Connection ID
            update_data: Update data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            update_dict = {"updated_at": datetime.now(timezone.utc)}
            
            # Handle encrypted fields
            if update_data.access_token:
                update_dict["access_token_encrypted"] = token_manager.encryption.encrypt_token(
                    update_data.access_token
                )
            
            if update_data.refresh_token:
                update_dict["refresh_token_encrypted"] = token_manager.encryption.encrypt_token(
                    update_data.refresh_token
                )
            
            if update_data.client_secret:
                update_dict["client_secret_encrypted"] = token_manager.encryption.encrypt_token(
                    update_data.client_secret
                )
            
            # Handle other fields
            if update_data.realm_id:
                update_dict["realm_id"] = update_data.realm_id
            
            if update_data.client_id:
                update_dict["client_id"] = update_data.client_id
            
            if update_data.token_expires_at:
                update_dict["token_expires_at"] = update_data.token_expires_at
            
            if update_data.environment:
                update_dict["environment"] = update_data.environment
            
            result = await self.connections_collection.update_one(
                {"_id": ObjectId(connection_id)},
                {"$set": update_dict}
            )
            
            if result.modified_count > 0:
                logger.info(f"Updated QuickBooks connection {connection_id}")
                return True
            else:
                logger.warning(f"No changes made to QuickBooks connection {connection_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating QuickBooks connection {connection_id}: {str(e)}")
            return False
    
    async def delete_connection(self, connection_id: str) -> bool:
        """
        Delete a QuickBooks connection.
        
        Args:
            connection_id: Connection ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = await self.connections_collection.delete_one({
                "_id": ObjectId(connection_id)
            })
            
            if result.deleted_count > 0:
                logger.info(f"Deleted QuickBooks connection {connection_id}")
                return True
            else:
                logger.warning(f"QuickBooks connection {connection_id} not found for deletion")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting QuickBooks connection {connection_id}: {str(e)}")
            return False
    
    async def list_connections(
        self, 
        skip: int = 0, 
        limit: int = 50, 
        active_only: bool = True
    ) -> Dict[str, Any]:
        """
        List QuickBooks connections with pagination.
        
        Args:
            skip: Number of connections to skip
            limit: Number of connections to return
            active_only: Only return active connections
            
        Returns:
            List of connections with metadata
        """
        try:
            query = {}
            if active_only:
                query["is_active"] = True
            
            # Get total count
            total = await self.connections_collection.count_documents(query)
            
            # Get connections
            cursor = self.connections_collection.find(query).skip(skip).limit(limit)
            connections = []
            
            async for connection in cursor:
                # Don't include encrypted tokens in list response
                connection_data = {
                    "id": str(connection["_id"]),
                    "realm_id": connection["realm_id"],
                    "client_id": connection["client_id"],
                    "environment": connection["environment"],
                    "token_expires_at": connection["token_expires_at"],
                    "created_at": connection["created_at"],
                    "updated_at": connection["updated_at"],
                    "last_tested": connection.get("last_tested"),
                    "is_active": connection["is_active"],
                    "company_info": connection.get("company_info")
                }
                connections.append(connection_data)
            
            return {
                "success": True,
                "connections": connections,
                "total": total,
                "skip": skip,
                "limit": limit
            }
            
        except Exception as e:
            logger.error(f"Error listing QuickBooks connections: {str(e)}")
            return {
                "success": False,
                "error": "database_error",
                "message": str(e)
            }
    
    async def get_credentials(self, connection_id: str) -> Optional[QBCredentials]:
        """
        Get decrypted credentials for a connection.
        
        Args:
            connection_id: Connection ID
            
        Returns:
            Decrypted credentials if found, None otherwise
        """
        try:
            connection = await self.get_connection(connection_id)
            if not connection:
                return None
            
            # Decrypt sensitive data
            access_token = token_manager.encryption.decrypt_token(
                connection.access_token_encrypted
            )
            refresh_token = token_manager.encryption.decrypt_token(
                connection.refresh_token_encrypted
            )
            client_secret = token_manager.encryption.decrypt_token(
                connection.client_secret_encrypted
            )
            
            return QBCredentials(
                realm_id=connection.realm_id,
                client_id=connection.client_id,
                client_secret=client_secret,
                access_token=access_token,
                refresh_token=refresh_token,
                token_expires_at=connection.token_expires_at,
                environment=connection.environment
            )
            
        except Exception as e:
            logger.error(f"Error getting credentials for connection {connection_id}: {str(e)}")
            return None
