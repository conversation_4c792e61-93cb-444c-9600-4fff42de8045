"""
Background service for monitoring and refreshing QuickBooks tokens.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import settings
from app.schemas.quickbooks_connection import QBConnectionUpdate
from app.services.quickbooks_database import QuickBooksDatabase
from app.services.quickbooks_oauth_client import QuickBooksOAuthClient

logger = logging.getLogger(__name__)


class QuickBooksTokenMonitor:
    """
    Background service for monitoring and refreshing QuickBooks tokens.
    
    This service runs continuously and:
    1. Monitors all active QuickBooks connections
    2. Identifies tokens that will expire within the buffer time
    3. Automatically refreshes tokens before they expire
    4. Handles refresh failures gracefully
    5. Logs all token operations for monitoring
    """
    
    def __init__(self, db: AsyncIOMotorDatabase):
        """
        Initialize the token monitor.
        
        Args:
            db: Database instance
        """
        self.db = db
        self.qb_db = QuickBooksDatabase(db)
        self.oauth_client = QuickBooksOAuthClient()
        
        # Configuration
        self.check_interval_minutes = 5  # Check every 5 minutes
        self.token_buffer_minutes = 10   # Refresh tokens 10 minutes before expiry
        self.max_retry_attempts = 3      # Maximum retry attempts for failed refreshes
        
        # State tracking
        self.is_running = False
        self.last_check_time = None
        self.refresh_stats = {
            "total_checks": 0,
            "tokens_refreshed": 0,
            "refresh_failures": 0,
            "last_refresh_time": None
        }
    
    async def start_monitoring(self):
        """Start the background token monitoring service."""
        if self.is_running:
            logger.warning("QuickBooks token monitor is already running")
            return
        
        self.is_running = True
        logger.info("Starting QuickBooks token monitoring service")
        
        try:
            while self.is_running:
                await self._check_and_refresh_tokens()
                
                # Wait for next check interval
                await asyncio.sleep(self.check_interval_minutes * 60)
                
        except asyncio.CancelledError:
            logger.info("QuickBooks token monitor was cancelled")
        except Exception as e:
            logger.error(f"Unexpected error in QuickBooks token monitor: {str(e)}")
        finally:
            self.is_running = False
            logger.info("QuickBooks token monitoring service stopped")
    
    async def stop_monitoring(self):
        """Stop the background token monitoring service."""
        if not self.is_running:
            logger.warning("QuickBooks token monitor is not running")
            return
        
        logger.info("Stopping QuickBooks token monitoring service")
        self.is_running = False
    
    async def _check_and_refresh_tokens(self):
        """Check all connections and refresh tokens that are near expiry."""
        try:
            self.refresh_stats["total_checks"] += 1
            self.last_check_time = datetime.now(timezone.utc)
            
            logger.debug("Checking QuickBooks tokens for refresh")
            
            # Get all active connections
            connections_result = await self.qb_db.list_connections(
                skip=0, 
                limit=1000,  # Get all connections
                active_only=True
            )
            
            if not connections_result["success"]:
                logger.error("Failed to retrieve QuickBooks connections for token monitoring")
                return
            
            connections = connections_result["connections"]
            tokens_to_refresh = []
            
            # Check each connection for token expiry
            now = datetime.now(timezone.utc)
            buffer_time = now + timedelta(minutes=self.token_buffer_minutes)
            
            for connection in connections:
                token_expires_at = connection["token_expires_at"]
                
                # Convert to datetime if it's a string
                if isinstance(token_expires_at, str):
                    token_expires_at = datetime.fromisoformat(token_expires_at.replace('Z', '+00:00'))
                
                if token_expires_at <= buffer_time:
                    tokens_to_refresh.append(connection)
                    logger.info(f"QuickBooks connection {connection['id']} token expires at {token_expires_at}, scheduling refresh")
            
            # Refresh tokens that need it
            if tokens_to_refresh:
                logger.info(f"Found {len(tokens_to_refresh)} QuickBooks tokens to refresh")
                
                for connection in tokens_to_refresh:
                    await self._refresh_connection_token(connection)
            else:
                logger.debug("No QuickBooks tokens need refreshing")
                
        except Exception as e:
            logger.error(f"Error during QuickBooks token check: {str(e)}")
    
    async def _refresh_connection_token(self, connection: Dict[str, Any]):
        """
        Refresh the token for a specific connection.
        
        Args:
            connection: Connection data from database
        """
        connection_id = connection["id"]
        
        try:
            logger.info(f"Refreshing QuickBooks token for connection {connection_id}")
            
            # Get full connection credentials
            credentials = await self.qb_db.get_credentials(connection_id)
            if not credentials:
                logger.error(f"Could not retrieve credentials for QuickBooks connection {connection_id}")
                return
            
            # Attempt token refresh
            refresh_result = await self.oauth_client.refresh_access_token(
                credentials.refresh_token,
                credentials.client_id,
                credentials.client_secret
            )
            
            if refresh_result.success:
                # Update database with new tokens
                update_data = QBConnectionUpdate(
                    access_token=refresh_result.new_access_token,
                    refresh_token=refresh_result.new_refresh_token,
                    token_expires_at=refresh_result.new_expires_at
                )
                
                success = await self.qb_db.update_connection(connection_id, update_data)
                
                if success:
                    self.refresh_stats["tokens_refreshed"] += 1
                    self.refresh_stats["last_refresh_time"] = datetime.now(timezone.utc)
                    logger.info(f"Successfully refreshed QuickBooks token for connection {connection_id}")
                else:
                    logger.error(f"Failed to update database with refreshed token for connection {connection_id}")
                    self.refresh_stats["refresh_failures"] += 1
            else:
                logger.error(f"Failed to refresh QuickBooks token for connection {connection_id}: {refresh_result.message}")
                self.refresh_stats["refresh_failures"] += 1
                
                # If refresh failed due to invalid refresh token, mark connection as inactive
                if refresh_result.error_code in ["invalid_grant", "invalid_client"]:
                    logger.warning(f"Marking QuickBooks connection {connection_id} as inactive due to invalid credentials")
                    await self._deactivate_connection(connection_id)
                
        except Exception as e:
            logger.error(f"Unexpected error refreshing QuickBooks token for connection {connection_id}: {str(e)}")
            self.refresh_stats["refresh_failures"] += 1
    
    async def _deactivate_connection(self, connection_id: str):
        """
        Deactivate a connection that has invalid credentials.
        
        Args:
            connection_id: Connection ID to deactivate
        """
        try:
            # Update connection to mark as inactive
            update_data = QBConnectionUpdate()
            # Note: We would need to add is_active field to QBConnectionUpdate schema
            # For now, we'll just log the issue
            logger.warning(f"QuickBooks connection {connection_id} should be deactivated due to invalid credentials")
            
        except Exception as e:
            logger.error(f"Error deactivating QuickBooks connection {connection_id}: {str(e)}")
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """
        Get monitoring statistics.
        
        Returns:
            Dictionary with monitoring statistics
        """
        return {
            "is_running": self.is_running,
            "last_check_time": self.last_check_time,
            "check_interval_minutes": self.check_interval_minutes,
            "token_buffer_minutes": self.token_buffer_minutes,
            "stats": self.refresh_stats.copy()
        }
    
    async def force_check(self):
        """Force an immediate token check (for testing/debugging)."""
        logger.info("Forcing immediate QuickBooks token check")
        await self._check_and_refresh_tokens()


# Global instance for the token monitor
_token_monitor_instance = None


async def start_quickbooks_token_monitor(db: AsyncIOMotorDatabase):
    """
    Start the global QuickBooks token monitor instance.
    
    Args:
        db: Database instance
    """
    global _token_monitor_instance
    
    if _token_monitor_instance and _token_monitor_instance.is_running:
        logger.warning("QuickBooks token monitor is already running")
        return
    
    _token_monitor_instance = QuickBooksTokenMonitor(db)
    
    # Start monitoring in background task
    asyncio.create_task(_token_monitor_instance.start_monitoring())
    logger.info("QuickBooks token monitor started")


async def stop_quickbooks_token_monitor():
    """Stop the global QuickBooks token monitor instance."""
    global _token_monitor_instance
    
    if _token_monitor_instance:
        await _token_monitor_instance.stop_monitoring()
        logger.info("QuickBooks token monitor stopped")


def get_quickbooks_token_monitor_stats() -> Dict[str, Any]:
    """
    Get statistics from the global token monitor instance.
    
    Returns:
        Monitoring statistics or None if monitor is not running
    """
    global _token_monitor_instance
    
    if _token_monitor_instance:
        return _token_monitor_instance.get_monitoring_stats()
    else:
        return {"is_running": False, "error": "Monitor not initialized"}
