"""
Admin-based Outlook service that orchestrates Microsoft Graph API integration
"""

import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, UTC, timedelta
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import settings
from app.schemas.outlook import (
    OutlookConnectionCreate, OutlookConnectionUpdate, OutlookAuthUrlResponse,
    OutlookConnectionResponse, EmailFetchRequest, EmailFetchResponse,
    OutlookConnectionStatus, TokenInfo, OutlookConnectionInDB
)
from app.services.microsoft_graph_client import MicrosoftGraphClient
from app.services.outlook_database_admin import AdminOutlookDatabase
from app.services.outlook_email_service import OutlookEmailService
from app.services.token_manager import token_manager
from app.core.database import get_database

logger = logging.getLogger(__name__)


class AdminOutlookService:
    """Admin-based service for Outlook integration"""
    
    def __init__(self, database: AsyncIOMotorDatabase):
        """Initialize Outlook service with database connection"""
        self.database = database
        self.database_ops = AdminOutlookDatabase(database)
        self.email_service = OutlookEmailService(self.database_ops)
        self.graph_client = MicrosoftGraphClient()
        
        logger.info("AdminOutlookService initialized")
    
    async def initialize(self):
        """Initialize the service and perform startup tasks"""
        try:
            # Create database indexes for better performance
            await self._create_database_indexes()
            logger.info("AdminOutlookService initialization completed")
        except Exception as e:
            logger.error(f"Error during AdminOutlookService initialization: {str(e)}")
            raise
    
    async def shutdown(self):
        """Cleanup resources during shutdown"""
        try:
            logger.info("AdminOutlookService shutdown completed")
        except Exception as e:
            logger.error(f"Error during AdminOutlookService shutdown: {str(e)}")
    
    async def _create_database_indexes(self):
        """Create database indexes for better performance"""
        try:
            # Index for admin email lookups
            await self.database_ops.admin_collection.create_index("email", unique=True)
            
            # Index for outlook connections within admin documents
            await self.database_ops.admin_collection.create_index("outlook_connections.user_email")
            await self.database_ops.admin_collection.create_index("outlook_connections.connection_id")
            
            # Email tracking indexes (unchanged)
            await self.database_ops.email_tracking_collection.create_index([
                ("connection_id", 1), ("message_id", 1)
            ], unique=True)
            
            logger.info("Database indexes created successfully")
        except Exception as e:
            logger.warning(f"Error creating database indexes: {str(e)}")
    
    async def get_authorization_url(self, admin_email: str) -> OutlookAuthUrlResponse:
        """
        Generate Microsoft OAuth authorization URL with admin context
        
        Args:
            admin_email: Admin email address
            
        Returns:
            Authorization URL response with admin context in state
        """
        try:
            logger.info(f"Generating authorization URL for admin: {admin_email}")
            
            # Generate state parameter with admin email encoded
            state_data = {
                "admin_email": admin_email,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Get authorization URL from Microsoft Graph client
            auth_url, state = self.graph_client.get_authorization_url(json.dumps(state_data))
            auth_result = {
                "success": True,
                "auth_url": auth_url,
                "state": state
            }
            
            if auth_result["success"]:
                return OutlookAuthUrlResponse(
                    success=True,
                    auth_url=auth_result["auth_url"],
                    state=auth_result["state"],
                    message="Authorization URL generated successfully. Please visit the URL to grant access to your Outlook account."
                )
            else:
                logger.error(f"Failed to generate authorization URL: {auth_result.get('message')}")
                return OutlookAuthUrlResponse(
                    success=False,
                    message=f"Failed to generate authorization URL: {auth_result.get('message', 'Unknown error')}"
                )
                
        except Exception as e:
            logger.error(f"Error generating authorization URL: {str(e)}")
            return OutlookAuthUrlResponse(
                success=False,
                message=f"Error generating authorization URL: {str(e)}"
            )
    
    async def handle_oauth_callback(
        self, 
        admin_email: str, 
        authorization_code: str, 
        state: Optional[str] = None
    ) -> OutlookConnectionResponse:
        """
        Handle OAuth callback and create connection for admin
        
        Args:
            admin_email: Admin email address
            authorization_code: Authorization code from Microsoft
            state: State parameter for verification
            
        Returns:
            Connection response
        """
        try:
            logger.info(f"Handling OAuth callback for admin: {admin_email}")
            logger.debug(f"Authorization code: {authorization_code[:20]}...")
            logger.debug(f"Using redirect URI: {self.graph_client.redirect_uri}")

            # Exchange authorization code for tokens (must use same redirect URI as authorization)
            token_result = await self.graph_client.exchange_code_for_tokens(
                authorization_code,
                redirect_uri=self.graph_client.redirect_uri
            )

            logger.debug(f"Token exchange result success: {token_result.get('success')}")
            
            if not token_result["success"]:
                error_msg = token_result.get('error_description') or token_result.get('error') or token_result.get('message', 'Unknown error')
                logger.error(f"Token exchange failed: {error_msg}")
                return OutlookConnectionResponse(
                    success=False,
                    message=f"Token exchange failed: {error_msg}"
                )
            
            # Get user information
            access_token = token_result["access_token"]
            user_info_result = await self.graph_client.get_user_profile(access_token)

            if not user_info_result["success"]:
                logger.error(f"Failed to get user profile: {user_info_result.get('message')}")
                return OutlookConnectionResponse(
                    success=False,
                    message=f"Failed to get user profile: {user_info_result.get('message', 'Unknown error')}"
                )

            user_info = user_info_result["user_data"]
            user_email = user_info.get("mail") or user_info.get("userPrincipalName")
            
            if not user_email:
                logger.error("No email found in user info")
                return OutlookConnectionResponse(
                    success=False,
                    message="No email address found in user profile"
                )
            
            # Get token expiration from result (already calculated by graph client)
            expires_at = token_result.get("expires_at")
            
            # Create connection data
            connection_data = OutlookConnectionCreate(
                user_email=user_email,
                access_token=access_token,
                refresh_token=token_result["refresh_token"],
                token_expires_at=expires_at,
                scopes=settings.MICROSOFT_SCOPES
            )
            
            # Save connection to database
            create_result = await self.database_ops.create_connection(admin_email, connection_data)
            
            if not create_result["success"]:
                logger.error(f"Failed to create connection: {create_result.get('message')}")
                
                # If the error is because the connection already exists, get the existing connection
                if "already exists" in create_result.get("message", ""):
                    existing_connection = await self.database_ops.get_connection_by_email(admin_email, user_email)
                    if existing_connection:
                        # Auto-start email monitoring for existing connection if not running
                        try:
                            from app.services.email_monitor_service import get_email_monitor_service
                            monitor_service = get_email_monitor_service(self)

                            status = await monitor_service.get_monitoring_status()
                            if not status.get("is_running", False):
                                logger.info(f"Auto-starting email monitoring for existing connection {existing_connection.id}")
                                await monitor_service.start_monitoring()
                                logger.info(f"✅ Email monitoring started automatically for existing connection {existing_connection.id}")
                            else:
                                logger.info(f"Email monitoring already running for existing connection {existing_connection.id}")

                        except Exception as e:
                            logger.warning(f"Failed to auto-start email monitoring for existing connection {existing_connection.id}: {str(e)}")

                        return OutlookConnectionResponse(
                            success=True,
                            message=f"Connection already exists for {user_email}",
                            connection_id=str(existing_connection.id),
                            user_email=user_email,
                            expires_at=existing_connection.token_expires_at,
                            scopes=existing_connection.scopes
                        )
                
                return OutlookConnectionResponse(
                    success=False,
                    message=create_result.get("message", "Failed to create connection")
                )
            
            connection_id = create_result["connection_id"]

            logger.info(f"Successfully created Outlook connection {connection_id} for admin {admin_email}")

            # Auto-start email monitoring for the new connection
            try:
                from app.services.email_monitor_service import get_email_monitor_service
                monitor_service = get_email_monitor_service(self)

                # Check if monitoring is already running
                status = await monitor_service.get_monitoring_status()
                if not status.get("is_running", False):
                    logger.info(f"Auto-starting email monitoring for new connection {connection_id}")
                    await monitor_service.start_monitoring()
                    logger.info(f"✅ Email monitoring started automatically for connection {connection_id}")
                else:
                    logger.info(f"Email monitoring already running, new connection {connection_id} will be included")

            except Exception as e:
                logger.warning(f"Failed to auto-start email monitoring for connection {connection_id}: {str(e)}")
                # Don't fail the connection creation if monitoring fails to start

            return OutlookConnectionResponse(
                success=True,
                message=f"Successfully connected Outlook account {user_email}",
                connection_id=connection_id,
                user_email=user_email,
                expires_at=expires_at,
                scopes=settings.MICROSOFT_SCOPES
            )
            
        except Exception as e:
            logger.error(f"Error in OAuth callback: {str(e)}")
            return OutlookConnectionResponse(
                success=False,
                message=f"Authentication failed: {str(e)}"
            )
    
    async def get_connection_by_email(
        self, 
        admin_email: str, 
        user_email: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get connection by user email for admin
        
        Args:
            admin_email: Admin email address
            user_email: User email to search for
            
        Returns:
            Connection information or None if not found
        """
        try:
            connection = await self.database_ops.get_connection_by_email(admin_email, user_email)
            
            if not connection:
                return None
            
            return {
                "success": True,
                "connection_id": str(connection.id),
                "user_email": connection.user_email,
                "is_active": connection.is_active,
                "created_at": connection.created_at,
                "token_expires_at": connection.token_expires_at,
                "scopes": connection.scopes
            }
            
        except Exception as e:
            logger.error(f"Error getting connection by email: {str(e)}")
            return None

    async def list_connections(
        self,
        admin_email: str,
        skip: int = 0,
        limit: int = 50,
        active_only: bool = True
    ) -> Dict[str, Any]:
        """
        List all connections for admin

        Args:
            admin_email: Admin email address
            skip: Number of connections to skip
            limit: Maximum number of connections to return
            active_only: Only return active connections

        Returns:
            List of connections with metadata
        """
        try:
            return await self.database_ops.list_connections(admin_email, skip, limit, active_only)
        except Exception as e:
            logger.error(f"Error listing connections for admin {admin_email}: {str(e)}")
            return {
                "success": False,
                "error": "service_error",
                "message": str(e)
            }

    async def get_detailed_connections(
        self,
        admin_email: str,
        skip: int = 0,
        limit: int = 50,
        active_only: bool = True
    ) -> Dict[str, Any]:
        """
        Get detailed connection information for admin including email counts and health status

        Args:
            admin_email: Admin email address
            skip: Number of connections to skip
            limit: Maximum number of connections to return
            active_only: Only return active connections

        Returns:
            Detailed connection information with email counts and status
        """
        try:
            from app.schemas.outlook import OutlookConnectionDetail, OutlookConnectionDetailResponse
            from app.services.email_monitor_service import get_email_monitor_service
            from datetime import datetime, UTC

            # Get basic connections list
            connections_result = await self.database_ops.list_connections(admin_email, skip, limit, active_only)

            if not connections_result["success"]:
                return connections_result

            # Get monitoring service to check sync interval
            try:
                monitor_service = get_email_monitor_service(self)
                monitoring_status = await monitor_service.get_monitoring_status()
                sync_interval_seconds = monitoring_status.get("sync_interval", 60)
            except Exception as e:
                logger.warning(f"Could not get monitoring status: {str(e)}")
                sync_interval_seconds = 60  # Default fallback

            detailed_connections = []

            for connection in connections_result["connections"]:
                connection_id = connection["id"]

                try:
                    # Get email count for this connection
                    email_count = await self._get_connection_email_count(connection_id)

                    # Determine connection status and health
                    now = datetime.now(UTC)
                    token_expires_at = connection.get("token_expires_at")
                    is_token_valid = False
                    health_status = "unknown"
                    status = "Disconnected"
                    error_message = None

                    if token_expires_at:
                        # Ensure timezone awareness
                        if token_expires_at.tzinfo is None:
                            token_expires_at = token_expires_at.replace(tzinfo=UTC)

                        is_token_valid = token_expires_at > now

                        if connection.get("is_active", False) and is_token_valid:
                            status = "Connected"
                            health_status = "healthy"
                        elif connection.get("is_active", False) and not is_token_valid:
                            status = "Disconnected"
                            health_status = "token_expired"
                            error_message = "Access token has expired"
                        elif not connection.get("is_active", False):
                            status = "Disconnected"
                            health_status = "inactive"
                            error_message = "Connection is inactive"
                        else:
                            status = "Disconnected"
                            health_status = "error"
                            error_message = "Connection status unknown"
                    else:
                        error_message = "Token expiration information not available"
                        health_status = "error"

                    # Create detailed connection object
                    detailed_connection = OutlookConnectionDetail(
                        connection_id=connection_id,
                        user_email=connection["user_email"],
                        status=status,
                        is_active=connection.get("is_active", False),
                        is_token_valid=is_token_valid,
                        created_at=connection["created_at"],
                        last_sync_at=connection.get("last_sync_at"),
                        token_expires_at=token_expires_at,
                        email_count=email_count,
                        sync_interval_seconds=sync_interval_seconds,
                        scopes=connection.get("scopes", []),
                        error_message=error_message,
                        health_status=health_status
                    )

                    detailed_connections.append(detailed_connection)

                except Exception as e:
                    logger.error(f"Error getting details for connection {connection_id}: {str(e)}")
                    # Add connection with error status
                    detailed_connection = OutlookConnectionDetail(
                        connection_id=connection_id,
                        user_email=connection.get("user_email", "unknown"),
                        status="Error",
                        is_active=False,
                        is_token_valid=False,
                        created_at=connection.get("created_at", datetime.now(UTC)),
                        last_sync_at=connection.get("last_sync_at"),
                        token_expires_at=connection.get("token_expires_at"),
                        email_count=0,
                        sync_interval_seconds=sync_interval_seconds,
                        scopes=connection.get("scopes", []),
                        error_message=f"Error retrieving connection details: {str(e)}",
                        health_status="error"
                    )
                    detailed_connections.append(detailed_connection)

            # Count active connections
            active_count = sum(1 for conn in detailed_connections if conn.is_active)

            return {
                "success": True,
                "message": "Detailed connection information retrieved successfully",
                "admin_email": admin_email,
                "connections": [conn.dict() for conn in detailed_connections],
                "total_connections": connections_result["total"],
                "active_connections": active_count,
                "sync_interval_seconds": sync_interval_seconds
            }

        except Exception as e:
            logger.error(f"Error getting detailed connections for admin {admin_email}: {str(e)}")
            return {
                "success": False,
                "error": "service_error",
                "message": f"Failed to get detailed connections: {str(e)}"
            }

    async def _get_connection_email_count(self, connection_id: str) -> int:
        """
        Get the total number of emails for a connection

        Args:
            connection_id: Connection ID

        Returns:
            Total number of emails
        """
        try:
            from bson import ObjectId

            if not ObjectId.is_valid(connection_id):
                logger.warning(f"Invalid connection ID for email count: {connection_id}")
                return 0

            connection_obj_id = ObjectId(connection_id)

            # Count emails in the admin collection (embedded emails array)
            # This is where emails are actually stored in this system
            try:
                # Use aggregation to count emails in the specific connection's emails array
                pipeline = [
                    {"$match": {"outlook_connections.connection_id": connection_obj_id}},
                    {"$unwind": "$outlook_connections"},
                    {"$match": {"outlook_connections.connection_id": connection_obj_id}},
                    {"$project": {
                        "email_count": {"$size": {"$ifNull": ["$outlook_connections.emails", []]}}
                    }}
                ]

                cursor = self.database_ops.admin_collection.aggregate(pipeline)
                result = await cursor.to_list(length=1)

                if result:
                    email_count = result[0].get("email_count", 0)
                    logger.debug(f"Email count for connection {connection_id}: {email_count}")
                    return email_count
                else:
                    logger.debug(f"No matching connection found for ID: {connection_id}")
                    return 0

            except Exception as e:
                logger.error(f"Error counting emails in admin collection for connection {connection_id}: {str(e)}")
                return 0

        except Exception as e:
            logger.error(f"Error getting email count for connection {connection_id}: {str(e)}")
            return 0

    async def get_connection_status(
        self,
        admin_email: str,
        connection_id: str
    ) -> OutlookConnectionStatus:
        """
        Get connection status for admin

        Args:
            admin_email: Admin email address
            connection_id: Connection ID

        Returns:
            Connection status information
        """
        try:
            connection = await self.database_ops.get_connection(admin_email, connection_id)

            if not connection:
                return OutlookConnectionStatus(
                    success=False,
                    message="Connection not found",
                    connection_id=connection_id,
                    is_active=False,
                    is_token_valid=False
                )

            # Check token validity
            is_token_valid = connection.token_expires_at > datetime.now(UTC)

            return OutlookConnectionStatus(
                success=True,
                message="Connection status retrieved successfully",
                connection_id=connection_id,
                user_email=connection.user_email,
                is_active=connection.is_active,
                is_token_valid=is_token_valid,
                token_expires_at=connection.token_expires_at,
                last_sync_at=connection.last_sync_at,
                scopes=connection.scopes
            )

        except Exception as e:
            logger.error(f"Error getting connection status: {str(e)}")
            return OutlookConnectionStatus(
                success=False,
                message=f"Error getting connection status: {str(e)}",
                connection_id=connection_id,
                is_active=False,
                is_token_valid=False
            )

    async def refresh_connection_token(
        self,
        admin_email: str,
        connection_id: str
    ) -> Dict[str, Any]:
        """
        Refresh access token for connection

        Args:
            admin_email: Admin email address
            connection_id: Connection ID

        Returns:
            Refresh result
        """
        try:
            connection = await self.database_ops.get_connection(admin_email, connection_id)

            if not connection:
                return {
                    "success": False,
                    "message": "Connection not found"
                }

            # Decrypt refresh token
            refresh_token = token_manager.encryption.decrypt_token(
                connection.refresh_token_encrypted
            )

            # Refresh token using Microsoft Graph client
            refresh_result = await self.graph_client.refresh_access_token(refresh_token)

            if not refresh_result["success"]:
                logger.error(f"Token refresh failed: {refresh_result.get('message')}")
                return refresh_result

            # Update connection with new tokens
            expires_in = refresh_result.get("expires_in", 3600)
            expires_at = datetime.now(UTC) + timedelta(seconds=expires_in)

            update_data = OutlookConnectionUpdate(
                access_token=refresh_result["access_token"],
                refresh_token=refresh_result.get("refresh_token", refresh_token),
                token_expires_at=expires_at
            )

            update_success = await self.database_ops.update_connection(
                admin_email, connection_id, update_data
            )

            if update_success:
                logger.info(f"Successfully refreshed token for connection {connection_id}")
                return {
                    "success": True,
                    "message": "Token refreshed successfully",
                    "expires_at": expires_at
                }
            else:
                return {
                    "success": False,
                    "message": "Failed to update connection with new token"
                }

        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            return {
                "success": False,
                "message": f"Token refresh failed: {str(e)}"
            }

    async def delete_connection(self, admin_email: str, connection_id: str) -> Dict[str, Any]:
        """
        Delete a specific Outlook connection for an admin.

        Args:
            admin_email: Email of the admin
            connection_id: ID of the connection to delete

        Returns:
            Dict with deletion result
        """
        try:
            logger.info(f"Deleting connection {connection_id} for admin {admin_email}")

            # Use database operations to delete the connection
            success = await self.database_ops.delete_connection(admin_email, connection_id)

            if success:
                logger.info(f"Successfully deleted connection {connection_id} for admin {admin_email}")
                return {
                    "success": True,
                    "message": f"Connection {connection_id} deleted successfully",
                    "connection_id": connection_id
                }
            else:
                logger.warning(f"Connection {connection_id} not found for admin {admin_email}")
                return {
                    "success": False,
                    "message": "Connection not found",
                    "connection_id": connection_id
                }

        except Exception as e:
            logger.error(f"Error deleting connection {connection_id}: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to delete connection: {str(e)}",
                "connection_id": connection_id
            }

    async def fetch_emails(self, connection_id: str, request: Any = None) -> Any:
        """
        Fetch emails for a specific connection (for email monitoring service compatibility)

        Args:
            connection_id: The connection ID to fetch emails for
            request: Email fetch request object (optional, defaults to basic fetch)

        Returns:
            Email fetch result
        """
        try:
            # Get the connection to find the admin email
            connection = await self.database_ops.get_connection_by_id(connection_id)
            if not connection:
                logger.error(f"Connection {connection_id} not found")
                return type('Result', (), {'success': False, 'message': 'Connection not found'})()

            # If no request provided (e.g., from monitoring service), create a default one
            if request is None:
                from app.schemas.outlook import EmailFetchRequest
                request = EmailFetchRequest(
                    user_email=connection.user_email,  # Use the connection's email
                    limit=settings.EMAIL_FETCH_BATCH_SIZE,  # Use configurable limit from settings
                    include_attachments=True,  # Include attachments by default
                    folder="inbox"  # Default folder
                )

            # Get access token for the connection
            access_token = await self._get_valid_access_token(connection)
            if not access_token:
                logger.error(f"Failed to get valid access token for connection {connection_id}")
                return type('Result', (), {'success': False, 'message': 'Failed to get valid access token'})()

            # Use the email service to fetch emails
            result = await self.email_service.fetch_emails(connection_id, access_token, request)
            return result

        except Exception as e:
            logger.error(f"Error fetching emails for connection {connection_id}: {str(e)}")
            return type('Result', (), {'success': False, 'message': f'Error fetching emails: {str(e)}'})()

    async def _get_valid_access_token(self, connection: OutlookConnectionInDB) -> Optional[str]:
        """
        Get a valid access token for the connection, refreshing if necessary

        Args:
            connection: The connection object

        Returns:
            Valid access token or None if failed
        """
        try:
            # Check if token is expired or near expiry
            now = datetime.now(UTC)
            token_expires_at = connection.token_expires_at
            if token_expires_at.tzinfo is None:
                token_expires_at = token_expires_at.replace(tzinfo=UTC)

            # If token is still valid (more than 5 minutes remaining)
            if token_expires_at > now + timedelta(minutes=5):
                # Decrypt and return current access token
                return token_manager.encryption.decrypt_token(connection.access_token_encrypted)

            # Token needs refresh
            logger.info(f"Refreshing token for connection: {connection.id}")

            # Decrypt refresh token
            refresh_token = token_manager.encryption.decrypt_token(connection.refresh_token_encrypted)

            # Refresh token
            refresh_result = await self.graph_client.refresh_access_token(refresh_token)

            if not refresh_result.success:
                logger.error(f"Token refresh failed: {refresh_result.message}")
                return None

            # Update connection with new tokens in admin collection
            await self._update_connection_tokens(
                str(connection.id),
                refresh_result.new_access_token,
                refresh_result.new_refresh_token,
                refresh_result.new_expires_at
            )

            logger.info(f"Token refreshed successfully for connection: {connection.id}")
            return refresh_result.new_access_token

        except Exception as e:
            logger.error(f"Error getting valid access token: {str(e)}")
            return None

    async def _update_connection_tokens(self, connection_id: str, access_token: str, refresh_token: str, expires_at: datetime):
        """
        Update connection tokens in the admin collection

        Args:
            connection_id: Connection ID
            access_token: New access token
            refresh_token: New refresh token
            expires_at: Token expiration time
        """
        try:
            from bson import ObjectId

            # Encrypt new tokens
            encrypted_access_token = token_manager.encryption.encrypt_token(access_token)
            encrypted_refresh_token = token_manager.encryption.encrypt_token(refresh_token)

            # Convert connection_id to ObjectId if needed
            try:
                connection_obj_id = ObjectId(connection_id)
            except:
                connection_obj_id = connection_id

            # Update the specific connection in admin collection
            db = await get_database()
            admin_collection = db.admin

            result = await admin_collection.update_one(
                {"outlook_connections.connection_id": connection_obj_id},
                {
                    "$set": {
                        "outlook_connections.$.access_token_encrypted": encrypted_access_token,
                        "outlook_connections.$.refresh_token_encrypted": encrypted_refresh_token,
                        "outlook_connections.$.token_expires_at": expires_at,
                        "outlook_connections.$.updated_at": datetime.now(UTC)
                    }
                }
            )

            if result.modified_count == 0:
                logger.warning(f"No connection updated for ID: {connection_id}")
            else:
                logger.debug(f"Successfully updated tokens for connection: {connection_id}")

        except Exception as e:
            logger.error(f"Error updating connection tokens: {str(e)}")


# Dependency function for FastAPI
async def get_admin_outlook_service() -> AdminOutlookService:
    """
    FastAPI dependency to get AdminOutlookService instance.

    Returns:
        AdminOutlookService instance
    """
    from app.core.database import get_db

    db = await get_db()
    service = AdminOutlookService(db)
    await service.initialize()
    return service
