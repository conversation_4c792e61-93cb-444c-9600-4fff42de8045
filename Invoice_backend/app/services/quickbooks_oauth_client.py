"""
QuickBooks OAuth 2.0 client for handling token operations.
"""

import base64
import logging
import httpx
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any

from app.schemas.quickbooks_connection import TokenRefreshResult

logger = logging.getLogger(__name__)


class QuickBooksOAuthClient:
    """
    QuickBooks OAuth 2.0 client for handling token operations.
    
    This client handles:
    - Token refresh using refresh tokens
    - QuickBooks-specific OAuth 2.0 flow
    - Error handling and logging
    """
    
    def __init__(self):
        """Initialize QuickBooks OAuth client"""
        # QuickBooks OAuth 2.0 endpoints
        self.token_url = "https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer"
        self.discovery_url = "https://oauth.platform.intuit.com/oauth2/v1/discovery"
        
        # HTTP client with timeout
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def refresh_access_token(
        self, 
        refresh_token: str, 
        client_id: str, 
        client_secret: str
    ) -> TokenRefreshResult:
        """
        Refresh QuickBooks access token using refresh token.
        
        Args:
            refresh_token: The refresh token to use
            client_id: QuickBooks app client ID
            client_secret: QuickBooks app client secret
            
        Returns:
            TokenRefreshResult with new tokens or error information
        """
        try:
            logger.info("Attempting to refresh QuickBooks access token")
            
            # Prepare Basic authentication header
            credentials = f"{client_id}:{client_secret}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            
            # Prepare request headers
            headers = {
                "Authorization": f"Basic {encoded_credentials}",
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json"
            }
            
            # Prepare request data
            data = {
                "grant_type": "refresh_token",
                "refresh_token": refresh_token
            }
            
            # Make token refresh request
            response = await self.client.post(
                self.token_url,
                headers=headers,
                data=data
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # Calculate new expiration time
                expires_in = result.get("expires_in", 3600)  # Default 1 hour
                new_expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)
                
                logger.info("Successfully refreshed QuickBooks access token")
                return TokenRefreshResult(
                    success=True,
                    message="Token refreshed successfully",
                    new_access_token=result["access_token"],
                    new_refresh_token=result.get("refresh_token", refresh_token),
                    new_expires_at=new_expires_at
                )
            
            elif response.status_code == 400:
                # Bad request - likely invalid refresh token
                error_data = response.json() if response.content else {}
                error_msg = error_data.get("error_description", "Invalid refresh token")
                logger.error(f"QuickBooks token refresh failed: {error_msg}")
                return TokenRefreshResult(
                    success=False,
                    message=f"Token refresh failed: {error_msg}",
                    error_code="invalid_grant"
                )
            
            elif response.status_code == 401:
                # Unauthorized - likely invalid client credentials
                logger.error("QuickBooks token refresh failed: Invalid client credentials")
                return TokenRefreshResult(
                    success=False,
                    message="Token refresh failed: Invalid client credentials",
                    error_code="invalid_client"
                )
            
            else:
                # Other error
                error_text = response.text if response.content else "Unknown error"
                logger.error(f"QuickBooks token refresh failed: {response.status_code} - {error_text}")
                return TokenRefreshResult(
                    success=False,
                    message=f"Token refresh failed: HTTP {response.status_code}",
                    error_code="http_error"
                )
                
        except httpx.TimeoutException:
            logger.error("QuickBooks token refresh timed out")
            return TokenRefreshResult(
                success=False,
                message="Token refresh timed out",
                error_code="timeout"
            )
            
        except httpx.RequestError as e:
            logger.error(f"QuickBooks token refresh network error: {str(e)}")
            return TokenRefreshResult(
                success=False,
                message=f"Network error during token refresh: {str(e)}",
                error_code="network_error"
            )
            
        except Exception as e:
            logger.error(f"Unexpected error during QuickBooks token refresh: {str(e)}")
            return TokenRefreshResult(
                success=False,
                message=f"Unexpected error: {str(e)}",
                error_code="unexpected_error"
            )
    
    async def validate_token(self, access_token: str, realm_id: str) -> bool:
        """
        Validate QuickBooks access token by making a test API call.
        
        Args:
            access_token: Access token to validate
            realm_id: QuickBooks company ID (realm ID)
            
        Returns:
            True if token is valid, False otherwise
        """
        try:
            # Test token with a simple company info request
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }
            
            # QuickBooks sandbox base URL
            base_url = f"https://sandbox-quickbooks.api.intuit.com/v3/company/{realm_id}"
            test_url = f"{base_url}/companyinfo/{realm_id}"
            
            response = await self.client.get(test_url, headers=headers)
            
            if response.status_code == 200:
                logger.info("QuickBooks token validation successful")
                return True
            else:
                logger.warning(f"QuickBooks token validation failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error validating QuickBooks token: {str(e)}")
            return False
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
