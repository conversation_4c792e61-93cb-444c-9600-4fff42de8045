"""
Main FastAPI application entry point
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
import logging

from app.core.config import settings
from app.core.database import connect_to_mongo, close_mongo_connection, get_database
from app.core.exceptions import add_exception_handlers
from app.core.logging_config import setup_logging, get_logger
from app.core.error_handlers import (
    ErrorHandlingMiddleware, http_exception_handler, validation_exception_handler,
    outlook_api_exception_handler, OutlookAPIException
)
from app.api.v1.api import api_router
from app.services.outlook_service import OutlookService
from app.services.outlook_service_admin import AdminOutlookService
from app.services.email_monitor_service import get_email_monitor_service
from app.services.attachment_service import get_attachment_service
from app.services.quickbooks_token_monitor import start_quickbooks_token_monitor, stop_quickbooks_token_monitor
import uvicorn

# Setup structured logging
setup_logging()
logger = get_logger(__name__)

def create_application() -> FastAPI:
    """Create and configure FastAPI application"""

    app = FastAPI(
        title=settings.PROJECT_NAME,
        description=settings.PROJECT_DESCRIPTION,
        version=settings.VERSION,
        openapi_url=f"{settings.API_V1_STR}/openapi.json"
    )

    # Add error handling middleware
    app.add_middleware(ErrorHandlingMiddleware)

    # Set up CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.BACKEND_CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add custom exception handlers
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(OutlookAPIException, outlook_api_exception_handler)

    # Add existing exception handlers
    add_exception_handlers(app)

    # Include API router
    app.include_router(api_router, prefix=settings.API_V1_STR)

    # Database connection events
    @app.on_event("startup")
    async def startup_event():
        """Initialize database connection and log startup info"""
        logger.info("=" * 50)
        logger.info(f"{settings.PROJECT_NAME} starting up...")
        logger.info("=" * 50)

        # Connect to MongoDB
        await connect_to_mongo()

        # Initialize Outlook service
        try:
            db = await get_database()
            outlook_service = OutlookService(db)
            await outlook_service.initialize()
            app.state.outlook_service = outlook_service
            logger.info("✅ Outlook service initialized successfully")

            # Initialize Admin Outlook Service for email monitoring
            admin_outlook_service = AdminOutlookService(db)
            await admin_outlook_service.initialize()
            app.state.admin_outlook_service = admin_outlook_service
            logger.info("✅ Admin Outlook service initialized successfully")

            # Initialize Email Monitor Service with admin service (but don't start it automatically)
            email_monitor = get_email_monitor_service(admin_outlook_service)
            app.state.email_monitor_service = email_monitor
            logger.info("✅ Email monitor service initialized (use /api/v1/outlook/monitor/start to begin monitoring)")

            # Initialize Attachment Service
            attachment_service = get_attachment_service()
            app.state.attachment_service = attachment_service
            logger.info("✅ Attachment service initialized successfully")

            # Initialize QuickBooks Token Monitor Service
            await start_quickbooks_token_monitor(db)
            logger.info("✅ QuickBooks token monitor service started")

        except Exception as e:
            logger.error(f"❌ Failed to initialize services: {e}")

        # Log application configuration
        logger.info("Application Configuration:")
        logger.info(f"- Title: {settings.PROJECT_NAME}")
        logger.info(f"- Version: {settings.VERSION}")
        logger.info(f"- Environment: {settings.ENVIRONMENT}")
        logger.info(f"- Debug Mode: {settings.DEBUG}")

        # Log database configuration
        logger.info("Database Configuration:")
        logger.info(f"- MongoDB URL: {settings.MONGODB_URL}")
        logger.info(f"- Database Name: {settings.DATABASE_NAME}")

        # Log QuickBooks configuration
        logger.info("QuickBooks Configuration:")
        logger.info("- Environment: Sandbox")
        logger.info(f"- Realm ID: {settings.QB_REALM_ID}")
        logger.info("- Credentials: ✅ Configured")

        # Log Microsoft Graph configuration
        logger.info("Microsoft Graph Configuration:")
        logger.info(f"- Client ID: {'✅ Configured' if settings.MICROSOFT_CLIENT_ID else '❌ Not configured'}")
        logger.info(f"- Tenant ID: {settings.MICROSOFT_TENANT_ID}")
        logger.info(f"- Redirect URI: {settings.MICROSOFT_REDIRECT_URI}")
        logger.info(f"- Scopes: {', '.join(settings.MICROSOFT_SCOPES)}")

        # Log available endpoints
        logger.info("Available Endpoints:")
        logger.info("- GET /health - Health check endpoint")
        logger.info("- GET /accounts - Fetch all accounting heads from QuickBooks")
        logger.info("- POST /bills - Create bill in QuickBooks")
        logger.info("- GET /bills - List all bills")
        logger.info("- GET /outlook/auth/url - Get Outlook authorization URL")
        logger.info("- GET /outlook/auth/callback - Handle Outlook OAuth callback")
        logger.info("- GET /outlook/connections - List Outlook connections")
        logger.info("- POST /outlook/connections/{id}/emails/fetch - Fetch emails")
        logger.info("- POST /quickbooks/connections - Create QuickBooks connection")
        logger.info("- GET /quickbooks/connections - List QuickBooks connections")
        logger.info("- POST /quickbooks/connections/{id}/test - Test QuickBooks connection")
        logger.info("- GET /attachments/content - Get attachment content")
        logger.info("- GET /attachments/list/{connection_id}/{email_id} - List email attachments")
        logger.info("- GET /docs - Swagger UI documentation")
        logger.info("- GET /redoc - ReDoc documentation")

        logger.info("=" * 50)
        logger.info("🚀 Application startup complete!")
        logger.info(f"📖 Visit http://localhost:{settings.PORT}/docs for interactive API documentation")
        logger.info("=" * 50)

    @app.on_event("shutdown")
    async def shutdown_event():
        """Close database connection and cleanup services"""
        logger.info("Shutting down application...")

        # Shutdown Email Monitor service
        if hasattr(app.state, 'email_monitor_service'):
            try:
                email_monitor = app.state.email_monitor_service
                if email_monitor.is_running:
                    await email_monitor.stop_monitoring()
                logger.info("✅ Email monitor service shutdown complete")
            except Exception as e:
                logger.error(f"❌ Error shutting down email monitor service: {e}")

        # Shutdown QuickBooks Token Monitor service
        try:
            await stop_quickbooks_token_monitor()
            logger.info("✅ QuickBooks token monitor service shutdown complete")
        except Exception as e:
            logger.error(f"❌ Error shutting down QuickBooks token monitor service: {e}")

        # Close MongoDB connection
        await close_mongo_connection()
        logger.info("✅ Database connection closed")
        
        logger.info("=" * 50)
        logger.info("Application shutdown complete")
        logger.info("=" * 50)

    return app


app = create_application()



if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )