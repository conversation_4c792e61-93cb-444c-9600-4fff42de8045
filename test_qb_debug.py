#!/usr/bin/env python3
"""
Test script to debug QuickBooks connection creation issue
"""

import requests
import json

def get_admin_token():
    """Get admin authentication token"""
    login_data = {
        "email": "<EMAIL>",
        "password": "1234"
    }
    
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/admin/login",
            json=login_data,
            timeout=10
        )
        
        print(f"Login Status Code: {response.status_code}")
        print(f"Login Response: {response.text}")
        
        if response.status_code == 200:
            return response.json()["access_token"]
        else:
            print(f"Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Login error: {e}")
        return None

def test_qb_connection():
    """Test QuickBooks connection creation"""
    # Get admin token
    print("Getting admin token...")
    token = get_admin_token()
    if not token:
        print("Failed to get admin token")
        return
    
    print(f"Got token: {token[:20]}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "realm_id": "9341455012370538",  # Original realm_id to test existing connection
        "client_id": "ABUrtQN3bOciG1SRaCJJnJAs1cMc1vWkgV3EbRjw4GwVaE6W0x",
        "client_secret": "70DDjvCg9RJ9KjgrNpmODtbgxLNnPF5IJIhqTnBb",
        "access_token": "eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9",
        "refresh_token": "RT1-38-H0-1762602646zekkqvqrwkehy7w15l8m",
        "token_expires_at": "2025-09-30T11:41:48.005Z",
        "environment": "sandbox"
    }
    
    # Test connection creation
    print("Testing QuickBooks connection creation...")
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/quickbooks/connections",
            json=payload,
            headers=headers,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Connection created successfully!")
        else:
            print("❌ Connection creation failed")
            
    except Exception as e:
        print(f"Request error: {e}")

if __name__ == "__main__":
    test_qb_connection()
