#!/usr/bin/env python3
"""
Test script to verify QuickBooks connection creation works
"""

import requests
import json
from datetime import datetime

# Test payload from user
payload = {
    "realm_id": "9341455012370538",
    "client_id": "ABUrtQN3bOciG1SRaCJJnJAs1cMc1vWkgV3EbRjw4GwVaE6W0x",
    "client_secret": "70DDjvCg9RJ9KjgrNpmODtbgxLNnPF5IJIhqTnBb",
    "access_token": "eyJhbGciOiJkaXIiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwieC5vcmciOiJIMCJ9",
    "refresh_token": "RT1-38-H0-1762602646zekkqvqrwkehy7w15l8m",
    "token_expires_at": "2025-09-30T11:41:48.005Z",
    "environment": "sandbox"
}

# First, get admin token
def get_admin_token():
    """Get admin authentication token"""
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"  # This should be the correct admin password
    }
    
    response = requests.post(
        "http://127.0.0.1:8000/api/v1/admin/login",
        json=login_data
    )
    
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def test_qb_connection():
    """Test QuickBooks connection creation"""
    # Get admin token
    token = get_admin_token()
    if not token:
        print("Failed to get admin token")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test connection creation
    response = requests.post(
        "http://127.0.0.1:8000/api/v1/quickbooks/connections",
        json=payload,
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

if __name__ == "__main__":
    test_qb_connection()
